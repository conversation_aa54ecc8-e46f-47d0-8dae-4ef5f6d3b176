<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>接入规则配置 - 视频会议服务管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="assets/css/site-management.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out" id="sidebar">
        <div class="flex items-center justify-center h-16 bg-blue-600">
            <h1 class="text-white text-lg font-semibold">视频会议管理系统</h1>
        </div>
        <nav class="mt-8">
            <div class="px-4 space-y-2">
                <a href="site-management.html" class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg">
                    <i class="fas fa-server mr-3"></i>
                    站点管理
                </a>
            </div>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="lg:ml-64">
        <!-- 顶部导航栏 -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center">
                    <button class="lg:hidden text-gray-500 hover:text-gray-700" id="menu-toggle">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <nav class="ml-4 flex items-center space-x-2 text-sm text-gray-500">
                        <a href="#" class="hover:text-blue-600">首页</a>
                        <i class="fas fa-chevron-right text-xs"></i>
                        <a href="site-management.html" class="hover:text-blue-600">站点管理</a>
                        <i class="fas fa-chevron-right text-xs"></i>
                        <span class="text-gray-900">接入规则配置</span>
                    </nav>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-bell text-lg"></i>
                    </button>
                    <div class="flex items-center space-x-2">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="用户头像" class="w-8 h-8 rounded-full">
                        <span class="text-sm text-gray-700">管理员</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 页面内容 -->
        <main class="site-management-container">
            <!-- 页面标题 -->
            <div class="page-header">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="page-title">接入规则配置</h1>
                        <p class="page-subtitle">以匹配对象为中心统一管理会议室、终端IP、组织部门的站点分配规则</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="btn btn-secondary" onclick="location.href='site-management.html'">
                            <i class="fas fa-arrow-left"></i>
                            返回站点管理
                        </button>
                    </div>
                </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
                <div class="toolbar-left">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索规则..." id="searchInput">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select" id="typeFilter">
                            <option value="">所有类型</option>
                            <option value="meeting">会议室</option>
                            <option value="ip">终端/代理IP</option>
                            <option value="department">部门</option>
                        </select>
                        <select class="filter-select" id="siteFilter">
                            <option value="">所有站点</option>
                            <!-- 站点选项将通过JavaScript动态生成 -->
                        </select>

                    </div>
                </div>
                <div class="toolbar-right">

                    <button class="btn btn-primary" onclick="showAddRuleModal()">
                        <i class="fas fa-plus"></i>
                        新增规则
                    </button>
                    <button class="btn btn-success" onclick="showBatchImportModal()">
                        <i class="fas fa-upload"></i>
                        批量导入
                    </button>
                </div>
            </div>

            <!-- 规则优先级说明 -->
            <div class="card mb-6">
                <div class="card-header">
                    <h3 class="card-title">规则优先级说明</h3>
                </div>
                <div class="card-body">
                    <div class="flex items-center justify-between text-sm">
                        <div class="flex items-center space-x-8">
                            <div class="flex items-center space-x-2">
                                <div class="w-4 h-4 bg-red-500 rounded-full"></div>
                                <span class="font-medium">会议室规则</span>
                                <span class="text-gray-500">优先级最高</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-4 h-4 bg-yellow-500 rounded-full"></div>
                                <span class="font-medium">终端/代理IP规则</span>
                                <span class="text-gray-500">优先级中等</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-4 h-4 bg-blue-500 rounded-full"></div>
                                <span class="font-medium">部门规则</span>
                                <span class="text-gray-500">优先级最低</span>
                            </div>
                        </div>
                        <div class="text-gray-500">
                            匹配时按优先级顺序：会议室 > 终端/代理IP > 部门
                        </div>
                    </div>
                </div>
            </div>

            <!-- 规则列表 -->
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th data-sort="type">规则类型</th>
                            <th data-sort="name">规则名称</th>
                            <th data-sort="value">匹配对象</th>
                            <th data-sort="siteName">分配站点</th>
                        <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="rulesTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    显示第 <span id="pageStart">1</span>-<span id="pageEnd">10</span> 条，共 <span id="totalCount">0</span> 条记录
                </div>
                <button class="pagination-btn" id="prevPage" onclick="changePage(-1)">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div id="pageNumbers"></div>
                <button class="pagination-btn" id="nextPage" onclick="changePage(1)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </main>
    </div>

    <!-- 新增规则模态框 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 hidden z-50" id="addRuleModal">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
                <div class="flex items-center justify-between p-6 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">新增规则</h3>
                    <button class="text-gray-400 hover:text-gray-600" onclick="hideAddRuleModal()">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <form class="p-6" id="addRuleForm">
                    <div class="form-group">
                        <label class="form-label required">规则类型</label>
                        <select class="form-control" name="ruleType" id="ruleTypeSelect" onchange="updateRuleForm()" required>
                            <option value="">请选择规则类型</option>
                            <option value="meeting">会议室规则</option>
                            <option value="ip">终端/代理IP规则</option>
                            <option value="department">部门规则</option>
                        </select>
                        <div id="ruleTypeHint" class="form-help mt-2 text-sm text-blue-600 hidden"></div>
                    </div>

                    <div id="ruleFormContent">
                        <!-- 动态表单内容 -->
                    </div>

                    <div class="form-group">
                        <label class="form-label required">分配站点</label>
                        <select class="form-control" name="siteId" required onchange="checkSiteCapabilityInObjectRules()">
                            <option value="">请选择站点</option>
                            <!-- 站点选项将通过JavaScript动态生成 -->
                        </select>
                        <div id="siteCapabilityWarningObjectRules" class="hidden mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div class="flex items-start">
                                <i class="fas fa-exclamation-triangle text-yellow-600 mr-2 mt-0.5"></i>
                                <div class="text-sm text-yellow-800" id="warningTextObjectRules"></div>
                            </div>
                        </div>
                    </div>



                    <div class="flex justify-end space-x-3 pt-4 border-t">
                        <button type="button" class="btn btn-secondary" onclick="hideAddRuleModal()">取消</button>
                        <button type="submit" class="btn btn-primary">确认添加</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 批量导入模态框 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 hidden z-50" id="batchImportModal">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="flex items-center justify-between p-6 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">批量导入规则</h3>
                    <button class="text-gray-400 hover:text-gray-600" onclick="hideBatchImportModal()">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <div class="flex items-center justify-between mb-2">
                            <label class="form-label">选择文件</label>
                            <a href="#" class="text-blue-600 text-sm hover:underline" onclick="downloadTemplate()">
                                <i class="fas fa-download mr-1"></i>
                                下载模板
                            </a>
                        </div>
                        <input type="file" class="form-control" accept=".csv,.xlsx" id="importFile">
                        <div class="form-help">支持CSV和Excel格式，请按照模板格式准备数据</div>
                    </div>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                        <h4 class="font-medium text-yellow-800 mb-2">导入说明：</h4>
                        <ul class="text-sm text-yellow-700 space-y-1">
                            <li>• 文件格式：规则类型,规则名称,匹配对象,站点编码,服务类型,优先级,描述</li>
                            <li>• 规则类型：meeting(会议室)、ip(终端/代理IP)、department(部门)</li>
                            <li>• 服务类型：call(呼叫)、recording(录制)、vod(点播)，多个用分号分隔</li>
                            <li>• 重复规则将被跳过，无效数据将在导入结果中显示</li>
                        </ul>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <h4 class="font-medium text-blue-800 mb-2">IP地址格式说明：</h4>
                        <div class="text-sm text-blue-700 space-y-1">
                            <div><strong>地址格式:</strong></div>
                            <div>• IPv4: *******[/24], 1.2-4.3-5.4[/24]</div>
                            <div>• IPv6: 1:2:3:4::[/24], 1:2-4:3-5:4::[/24]</div>
                            <div class="text-blue-600">• IPv6段禁止0开头 (例: 1:1:1::而非0001:0001:0001::)</div>
                            <div><strong>生效时间:</strong></div>
                            <div>• IP段: 10分钟</div>
                            <div>• IP: 立即</div>
                            <div><strong>注意事项:</strong></div>
                            <div>• 范围地址重复: 添加"-"格式地址范围不校验重复</div>
                            <div>• 例: *******=86 和 1.2-4.3.4=87 都含 *******</div>
                            <div>• 系统遵循最长匹配: *******/24=86 和 *******/32=87，******* 匹配 *******/32=87</div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" class="btn btn-secondary" onclick="hideBatchImportModal()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="handleBatchImport()">开始导入</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script src="assets/js/object-rules.js"></script>
</body>
</html>
