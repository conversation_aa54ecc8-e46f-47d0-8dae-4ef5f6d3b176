<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>站点管理 - 视频会议服务管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="assets/css/site-management.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out" id="sidebar">
        <div class="flex items-center justify-center h-16 bg-blue-600">
            <h1 class="text-white text-lg font-semibold">视频会议管理系统</h1>
        </div>
        <nav class="mt-8">
            <div class="px-4 space-y-2">
                <a href="site-management.html" class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg">
                    <i class="fas fa-server mr-3"></i>
                    站点管理
                </a>
                <a href="metadata-management.html" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600">
                    <i class="fas fa-cogs mr-3"></i>
                    元数据管理
                </a>
            </div>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="lg:ml-64">
        <!-- 顶部导航栏 -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center">
                    <button class="lg:hidden text-gray-500 hover:text-gray-700" id="menu-toggle">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <nav class="ml-4 flex items-center space-x-2 text-sm text-gray-500">
                        <a href="#" class="hover:text-blue-600">首页</a>
                        <i class="fas fa-chevron-right text-xs"></i>
                        <span class="text-gray-900">站点管理</span>
                    </nav>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-bell text-lg"></i>
                    </button>
                    <div class="flex items-center space-x-2">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="用户头像" class="w-8 h-8 rounded-full">
                        <span class="text-sm text-gray-700">管理员</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 页面内容 -->
        <main class="site-management-container">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">站点管理</h1>
                <p class="page-subtitle">管理视频会议服务站点，配置站点属性和分配规则</p>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
                <div class="toolbar-left">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索站点编码、名称或区域..." id="searchInput">
                    </div>
                    <div class="filter-group">
                        <select class="filter-select" id="regionFilter">
                            <option value="">所有区域</option>
                            <option value="总部">总部</option>
                            <option value="郑州">郑州</option>
                            <option value="武汉">武汉</option>
                            <option value="西安">西安</option>
                        </select>
                        <select class="filter-select" id="networkFilter">
                            <option value="">所有网络</option>
                            <option value="内网">内网</option>
                            <option value="DMZ">DMZ</option>
                            <option value="互联网">互联网</option>
                        </select>
                    </div>
                </div>
                <div class="toolbar-right">
                    <button class="btn btn-success" onclick="location.href='object-rules.html'">
                        <i class="fas fa-cogs"></i>
                        接入规则配置
                    </button>
                    <button class="btn btn-primary" onclick="showAddSiteModal()">
                        <i class="fas fa-plus"></i>
                        新增站点
                    </button>
                </div>
            </div>

            <!-- 站点列表 -->
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th width="40"></th> <!-- 展开/收起按钮列 -->
                            <th data-sort="siteName">站点名称</th>
                            <th data-sort="region">所属区域</th>
                            <th data-sort="network">所属网络</th>
                            <th data-sort="serverCount">启动/绑定服务</th>
                            <th data-sort="backupSites">
                                <div class="flex items-center">
                                    备份站点
                                    <div class="tooltip ml-1">
                                        <i class="fas fa-info-circle tooltip-icon"></i>
                                        <span class="tooltip-text">资源不可用时将使用备份站点，优先使用排序靠前的站点</span>
                                        <span class="tooltip-arrow"></span>
                                    </div>
                                </div>
                            </th>
                            <th data-sort="rules">接入规则</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="siteTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    显示第 <span id="pageStart">1</span>-<span id="pageEnd">10</span> 条，共 <span id="totalCount">25</span> 条记录
                </div>
                <button class="pagination-btn" id="prevPage" onclick="changePage(-1)">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div id="pageNumbers"></div>
                <button class="pagination-btn" id="nextPage" onclick="changePage(1)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </main>
    </div>

    <!-- 站点详情侧边栏 -->
    <div class="fixed inset-y-0 right-0 z-50 w-96 bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out" id="sitePanel">
        <div class="flex flex-col h-full">
            <!-- 头部 -->
            <div class="flex items-center justify-between p-6 border-b">
                <h3 class="text-lg font-semibold text-gray-900" id="sitePanelTitle">站点详情</h3>
                <button class="text-gray-400 hover:text-gray-600" onclick="hideSitePanel()">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- 内容区域 -->
            <div class="flex-1 overflow-y-auto p-6">
                <div id="sitePanelContent">
                    <!-- 内容将通过JavaScript动态生成 -->
                </div>
            </div>

            <!-- 底部操作区 -->
            <div class="border-t p-6 hidden" id="sitePanelActions">
                <div class="flex justify-end space-x-3">
                    <button type="button" class="btn btn-secondary" onclick="cancelSiteEdit()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveSiteEdit()">保存</button>
                </div>
            </div>
        </div>
    </div>





    <!-- 新增站点模态框 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 hidden z-50" id="addSiteModal">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
                <div class="flex items-center justify-between p-6 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">新增站点</h3>
                    <button class="text-gray-400 hover:text-gray-600" onclick="hideAddSiteModal()">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <form class="p-6 space-y-4" id="addSiteForm">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="form-group">
                            <label class="form-label">站点编码</label>
                            <input type="text" class="form-control" name="siteCode" placeholder="如：86-PXYLINK（可选）">
                            <div class="form-help">可选，如不填写系统将自动生成</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">站点名称</label>
                            <input type="text" class="form-control" name="siteName" placeholder="输入站点名称" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">所属区域</label>
                            <select class="form-control" name="region" required>
                                <option value="">请选择区域</option>
                                <option value="总部">总部</option>
                                <option value="郑州">郑州</option>
                                <option value="武汉">武汉</option>
                                <option value="西安">西安</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">所属网络</label>
                            <select class="form-control" name="network" required>
                                <option value="">请选择网络</option>
                                <option value="内网">内网</option>
                                <option value="DMZ">DMZ</option>
                                <option value="互联网">互联网</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4 border-t">
                        <button type="button" class="btn btn-secondary" onclick="hideAddSiteModal()">取消</button>
                        <button type="submit" class="btn btn-primary">确认新增</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script src="assets/js/metadata-management.js"></script>
    <script src="assets/js/site-management.js"></script>
</body>
</html>
