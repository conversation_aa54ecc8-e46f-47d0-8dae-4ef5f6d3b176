# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a video conference service management system built with vanilla HTML, CSS, and JavaScript. The system manages conference service sites, their capabilities, backup configurations, and access rules for meeting rooms, IP addresses, and departments.

## Key Features

- **Site Management**: Create, edit, and manage video conference service sites
- **Capability Management**: Track call, recording, and VOD capabilities per site
- **Backup Site Configuration**: Configure backup sites for different service types
- **Object Rules Management**: Unified management of access rules for meeting rooms, terminal IPs, and departments
- **Rule Priority System**: Meeting room rules > IP rules > Department rules

## Architecture

### Core Components

1. **Site Management (`site-management.html` + `site-management.js`)**
   - Main site listing and management interface
   - Site detail sidebar with editing capabilities
   - Backup site management per service type
   - Rule configuration modal

2. **Object Rules (`object-rules.html` + `object-rules.js`)**
   - Unified rule management interface
   - Rule creation with dynamic forms based on type
   - Batch import functionality
   - Site capability validation

3. **Common Utilities (`common.js`)**
   - Message notification system
   - Confirmation dialogs
   - Utility functions (debounce, date formatting)

### Data Models

#### Site Structure
```javascript
{
  id: number,
  sitecode: string,        // Unique site identifier
  siteName: string,        // Display name
  region: string,          // Geographic region
  network: string,         // Network type (内网/DMZ/互联网)
  capabilities: array,     // ['call', 'recording', 'vod']
  serverCount: object,     // Active/total servers per capability
  servers: object,         // Server lists per capability
  backupSites: object      // Backup sites per capability
}
```

#### Rule Structure
```javascript
{
  id: number,
  type: string,           // 'meeting', 'ip', 'department'
  value: string,          // Meeting ID, IP range, or department name
  name: string,           // Display name (optional)
  siteId: number,         // Target site ID
  siteName: string,       // Target site name
  sitecode: string        // Target site code
}
```

## Development Commands

Since this is a static HTML/JS project, there are no build commands. Simply open the HTML files in a web browser:

- `open site-management.html` - Main site management interface
- `open object-rules.html` - Object rules management interface

## Key Business Logic

### Site Capability Validation
- Sites must have required capabilities for specific rule types
- Meeting rules require call + recording + VOD capabilities
- IP/Department rules require call capability
- Backup sites can provide missing capabilities

### Rule Priority System
1. **Meeting Room Rules** (highest priority) - Red indicators
2. **IP/Terminal Rules** (medium priority) - Yellow indicators
3. **Department Rules** (lowest priority) - Blue indicators

### Site Deletion Protection
- Sites with configured rules cannot be deleted
- Sites with bound servers cannot be deleted
- Core sites (like 86-PXYLINK) are protected from deletion

## File Structure

```
/
├── site-management.html    # Main site management interface
├── object-rules.html       # Object rules management interface
└── assets/
    ├── css/
    │   └── site-management.css  # All application styles
    └── js/
        ├── common.js            # Shared utilities and UI components
        ├── site-management.js   # Site management logic
        └── object-rules.js      # Object rules logic
```

## Important Implementation Details

### Mock Data System
- All data is currently mocked in JavaScript files
- `mockSites` array contains site configurations
- `mockRules` object contains rule mappings by site ID
- `mockObjectRules` array provides unified rule view

### UI Patterns
- Consistent use of Tailwind CSS for base styling
- Custom CSS variables for theming
- Responsive design with mobile considerations
- Modal dialogs for complex operations
- Slide-out panels for site details

### State Management
- Global state managed in JavaScript variables
- No external state management library
- UI updates through DOM manipulation
- Data persistence would require backend integration

## Testing Approach

Currently no automated testing framework is implemented. Manual testing can be performed by:

1. Opening the HTML files in a browser
2. Testing all CRUD operations for sites and rules
3. Validating capability checks and backup site logic
4. Testing rule priority and matching behavior

## Browser Compatibility

Built with modern web standards:
- ES6+ JavaScript features
- CSS Grid and Flexbox
- Font Awesome icons
- Tailwind CSS utilities

Target browsers: Chrome, Firefox, Safari, Edge (latest versions)