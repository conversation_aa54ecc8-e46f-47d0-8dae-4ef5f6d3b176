<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>元数据管理 - 视频会议服务管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="assets/css/site-management.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out" id="sidebar">
        <div class="flex items-center justify-center h-16 bg-blue-600">
            <h1 class="text-white text-lg font-semibold">视频会议管理系统</h1>
        </div>
        <nav class="mt-8">
            <div class="px-4 space-y-2">
                <a href="site-management.html" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600">
                    <i class="fas fa-server mr-3"></i>
                    站点管理
                </a>
                <a href="metadata-management.html" class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg">
                    <i class="fas fa-cogs mr-3"></i>
                    元数据管理
                </a>
            </div>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="lg:ml-64">
        <!-- 顶部导航栏 -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center">
                    <button class="lg:hidden text-gray-500 hover:text-gray-700" id="menu-toggle">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <nav class="ml-4 flex items-center space-x-2 text-sm text-gray-500">
                        <a href="#" class="hover:text-blue-600">首页</a>
                        <i class="fas fa-chevron-right text-xs"></i>
                        <span class="text-gray-900">元数据管理</span>
                    </nav>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-bell text-lg"></i>
                    </button>
                    <div class="flex items-center space-x-2">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="用户头像" class="w-8 h-8 rounded-full">
                        <span class="text-sm text-gray-700">管理员</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 页面内容 -->
        <main class="site-management-container">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">元数据管理</h1>
                <p class="page-subtitle">管理系统中的区域和网络元数据配置</p>
            </div>

            <!-- 标签页导航 -->
            <div class="bg-white rounded-lg shadow-sm mb-6">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex">
                        <button class="metadata-tab-btn active" data-tab="regions">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            区域管理
                        </button>
                        <button class="metadata-tab-btn" data-tab="networks">
                            <i class="fas fa-network-wired mr-2"></i>
                            网络管理
                        </button>
                    </nav>
                </div>

                <!-- 区域管理标签页 -->
                <div class="metadata-tab-content active" id="regions-tab">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-lg font-semibold text-gray-900">区域配置</h2>
                            <button class="btn btn-primary" onclick="showAddRegionModal()">
                                <i class="fas fa-plus mr-2"></i>
                                新增区域
                            </button>
                        </div>

                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>区域名称</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="regionsTableBody">
                                    <!-- 数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 网络管理标签页 -->
                <div class="metadata-tab-content" id="networks-tab">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-lg font-semibold text-gray-900">网络配置</h2>
                            <button class="btn btn-primary" onclick="showAddNetworkModal()">
                                <i class="fas fa-plus mr-2"></i>
                                新增网络
                            </button>
                        </div>

                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>网络名称</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="networksTableBody">
                                    <!-- 数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增区域模态框 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 hidden z-50" id="addRegionModal">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="flex items-center justify-between p-6 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">新增区域</h3>
                    <button class="text-gray-400 hover:text-gray-600" onclick="hideAddRegionModal()">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <form class="p-6 space-y-4" id="addRegionForm">
                    <div class="form-group">
                        <label class="form-label required">区域名称</label>
                        <input type="text" class="form-control" name="regionName" placeholder="如：总部" required>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4 border-t">
                        <button type="button" class="btn btn-secondary" onclick="hideAddRegionModal()">取消</button>
                        <button type="submit" class="btn btn-primary">确认新增</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 新增网络模态框 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 hidden z-50" id="addNetworkModal">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="flex items-center justify-between p-6 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">新增网络</h3>
                    <button class="text-gray-400 hover:text-gray-600" onclick="hideAddNetworkModal()">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <form class="p-6 space-y-4" id="addNetworkForm">
                    <div class="form-group">
                        <label class="form-label required">网络名称</label>
                        <input type="text" class="form-control" name="networkName" placeholder="如：办公网" required>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4 border-t">
                        <button type="button" class="btn btn-secondary" onclick="hideAddNetworkModal()">取消</button>
                        <button type="submit" class="btn btn-primary">确认新增</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 引入站点数据以便检查使用情况
        const mockSites = [
            {
                id: 1,
                sitecode: '86-PXYLINK',
                siteName: '总部主站点',
                region: '总部',
                network: '内网',
                capabilities: ['call', 'recording', 'vod']
            },
            {
                id: 2,
                sitecode: '87-PXYLINK',
                siteName: '郑州分站点',
                region: '郑州',
                network: 'DMZ',
                capabilities: ['call', 'recording']
            },
            {
                id: 3,
                sitecode: '88-PXYTEST',
                siteName: '测试站点',
                region: '武汉',
                network: '互联网',
                capabilities: ['call']
            },
            {
                id: 4,
                sitecode: '89-PXYDEV',
                siteName: '开发站点',
                region: '西安',
                network: '内网',
                capabilities: ['call', 'vod']
            },
            {
                id: 5,
                sitecode: '90-PXYBACKUP',
                siteName: '备份站点',
                region: '总部',
                network: 'DMZ',
                capabilities: ['call', 'recording', 'vod']
            }
        ];
    </script>
    <script src="assets/js/metadata-management.js"></script>
</body>
</html>
