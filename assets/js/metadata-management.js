// 元数据管理页面JavaScript

// 模拟数据
const mockRegions = [
    {
        id: 1,
        name: '总部'
    },
    {
        id: 2,
        name: '郑州'
    },
    {
        id: 3,
        name: '武汉'
    },
    {
        id: 4,
        name: '西安'
    }
];

const mockNetworks = [
    {
        id: 1,
        name: '内网'
    },
    {
        id: 2,
        name: 'DMZ'
    },
    {
        id: 3,
        name: '互联网'
    },
    {
        id: 4,
        name: '办公网'
    },
    {
        id: 5,
        name: '政务外网'
    },
    {
        id: 6,
        name: '指挥网'
    }
];

// 导出元数据函数供其他页面使用
function getRegions() {
    return mockRegions;
}

function getNetworks() {
    return mockNetworks;
}

function addRegion(name) {
    const newRegion = {
        id: mockRegions.length + 1,
        name: name
    };
    mockRegions.push(newRegion);
    return newRegion;
}

function addNetwork(name) {
    const newNetwork = {
        id: mockNetworks.length + 1,
        name: name
    };
    mockNetworks.push(newNetwork);
    return newNetwork;
}

function deleteRegion(id) {
    const index = mockRegions.findIndex(r => r.id === id);
    if (index > -1) {
        mockRegions.splice(index, 1);
        return true;
    }
    return false;
}

function deleteNetwork(id) {
    const index = mockNetworks.findIndex(n => n.id === id);
    if (index > -1) {
        mockNetworks.splice(index, 1);
        return true;
    }
    return false;
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    bindEvents();
    loadRegionsData();
    loadNetworksData();
});

function initializePage() {
    // 移动端菜单切换
    const menuToggle = document.getElementById('menu-toggle');
    const sidebar = document.getElementById('sidebar');

    if (menuToggle && sidebar) {
        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('-translate-x-full');
        });
    }
}

function bindEvents() {
    // 标签页切换事件
    const tabBtns = document.querySelectorAll('.metadata-tab-btn');
    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const tabName = btn.getAttribute('data-tab');
            switchTab(tabName);
        });
    });

    // 表单提交事件
    const addRegionForm = document.getElementById('addRegionForm');
    if (addRegionForm) {
        addRegionForm.addEventListener('submit', handleAddRegion);
    }

    const addNetworkForm = document.getElementById('addNetworkForm');
    if (addNetworkForm) {
        addNetworkForm.addEventListener('submit', handleAddNetwork);
    }
}

// 标签页切换
function switchTab(tabName) {
    // 更新按钮状态
    const tabBtns = document.querySelectorAll('.metadata-tab-btn');
    tabBtns.forEach(btn => {
        btn.classList.remove('active');
        if (btn.getAttribute('data-tab') === tabName) {
            btn.classList.add('active');
        }
    });

    // 更新内容显示
    const tabContents = document.querySelectorAll('.metadata-tab-content');
    tabContents.forEach(content => {
        content.classList.remove('active');
    });

    const targetTab = document.getElementById(`${tabName}-tab`);
    if (targetTab) {
        targetTab.classList.add('active');
    }
}

// 加载区域数据
function loadRegionsData() {
    const tbody = document.getElementById('regionsTableBody');
    if (!tbody) return;

    tbody.innerHTML = mockRegions.map(region => `
        <tr>
            <td>
                <div class="font-medium text-gray-900">${region.name}</div>
            </td>
            <td>
                <div class="flex items-center space-x-2">
                    <button class="btn-text btn-sm" onclick="editRegion(${region.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-text btn-sm text-red-600" onclick="deleteRegion(${region.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 加载网络数据
function loadNetworksData() {
    const tbody = document.getElementById('networksTableBody');
    if (!tbody) return;

    tbody.innerHTML = mockNetworks.map(network => `
        <tr>
            <td>
                <div class="font-medium text-gray-900">${network.name}</div>
            </td>
            <td>
                <div class="flex items-center space-x-2">
                    <button class="btn-text btn-sm" onclick="editNetwork(${network.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-text btn-sm text-red-600" onclick="deleteNetwork(${network.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}



// 区域管理功能
function showAddRegionModal() {
    document.getElementById('addRegionModal').classList.remove('hidden');
}

function hideAddRegionModal() {
    document.getElementById('addRegionModal').classList.add('hidden');
    document.getElementById('addRegionForm').reset();
}

function handleAddRegion(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const newRegion = {
        id: mockRegions.length + 1,
        name: formData.get('regionName')
    };

    // 验证区域名称唯一性
    if (mockRegions.some(region => region.name === newRegion.name)) {
        Message.error('区域名称已存在，请使用其他名称');
        return;
    }

    mockRegions.push(newRegion);
    hideAddRegionModal();
    loadRegionsData();
    Message.success('区域添加成功');
}

function editRegion(regionId) {
    Message.info('编辑区域功能开发中...');
}

function deleteRegion(regionId) {
    const region = mockRegions.find(r => r.id === regionId);
    if (!region) return;

    // 检查是否有站点使用该区域
    const relatedSites = checkRegionUsage(region.name);
    if (relatedSites.length > 0) {
        const siteNames = relatedSites.map(site => site.siteName).join('、');
        Message.error(`无法删除区域 "${region.name}"，以下站点正在使用该区域：${siteNames}`);
        return;
    }

    Confirm.show(
        `确定要删除区域 "${region.name}" 吗？此操作不可撤销。`,
        '确认删除',
        () => {
            const index = mockRegions.findIndex(r => r.id === regionId);
            if (index > -1) {
                mockRegions.splice(index, 1);
                loadRegionsData();
                Message.success('区域删除成功');
            }
        }
    );
}

// 网络管理功能
function showAddNetworkModal() {
    document.getElementById('addNetworkModal').classList.remove('hidden');
}

function hideAddNetworkModal() {
    document.getElementById('addNetworkModal').classList.add('hidden');
    document.getElementById('addNetworkForm').reset();
}

function handleAddNetwork(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const newNetwork = {
        id: mockNetworks.length + 1,
        name: formData.get('networkName')
    };

    // 验证网络名称唯一性
    if (mockNetworks.some(network => network.name === newNetwork.name)) {
        Message.error('网络名称已存在，请使用其他名称');
        return;
    }

    mockNetworks.push(newNetwork);
    hideAddNetworkModal();
    loadNetworksData();
    Message.success('网络添加成功');
}

function editNetwork(networkId) {
    Message.info('编辑网络功能开发中...');
}

function deleteNetwork(networkId) {
    const network = mockNetworks.find(n => n.id === networkId);
    if (!network) return;

    // 检查是否有站点使用该网络
    const relatedSites = checkNetworkUsage(network.name);
    if (relatedSites.length > 0) {
        const siteNames = relatedSites.map(site => site.siteName).join('、');
        Message.error(`无法删除网络 "${network.name}"，以下站点正在使用该网络：${siteNames}`);
        return;
    }

    Confirm.show(
        `确定要删除网络 "${network.name}" 吗？此操作不可撤销。`,
        '确认删除',
        () => {
            const index = mockNetworks.findIndex(n => n.id === networkId);
            if (index > -1) {
                mockNetworks.splice(index, 1);
                loadNetworksData();
                Message.success('网络删除成功');
            }
        }
    );
}

// 检查区域使用情况
function checkRegionUsage(regionName) {
    // 检查站点管理中是否有站点使用该区域
    if (typeof mockSites !== 'undefined') {
        return mockSites.filter(site => site.region === regionName);
    }
    return [];
}

// 检查网络使用情况
function checkNetworkUsage(networkName) {
    // 检查站点管理中是否有站点使用该网络
    if (typeof mockSites !== 'undefined') {
        return mockSites.filter(site => site.network === networkName);
    }
    return [];
}
