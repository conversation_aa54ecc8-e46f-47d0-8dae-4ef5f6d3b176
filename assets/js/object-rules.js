// 接入规则配置页面JavaScript

// 模拟站点数据
const mockSites = [
    {
        id: 1,
        sitecode: '86-PXYLINK',
        siteName: '总部主站点',
        region: '总部',
        network: '内网',
        capabilities: ['call', 'recording', 'vod']
    },
    {
        id: 2,
        sitecode: '87-PXYLINK',
        siteName: '郑州分站点',
        region: '郑州',
        network: 'DMZ',
        capabilities: ['call']
    },
    {
        id: 3,
        sitecode: '88-PXYTEST',
        siteName: '武汉测试站点',
        region: '武汉',
        network: '互联网',
        capabilities: ['recording', 'vod']
    },
    {
        id: 4,
        sitecode: '89-PXYWEST',
        siteName: '西安备份站点',
        region: '西安',
        network: '内网',
        capabilities: ['call', 'recording', 'vod']
    },
    {
        id: 5,
        sitecode: '90-PXYCLOUD',
        siteName: '云端站点',
        region: '总部',
        network: '互联网',
        capabilities: ['call', 'recording', 'vod']
    }
];

// 模拟会议号数据
const mockMeetings = {
    '123456789': 'VIP会议室',
    '987654321': '董事会会议室',
    '555666777': '郑州会议室',
    '888999000': '测试会议室',
    '111222333': '培训会议室',
    '999888777': '新建会议室'
};

// 模拟规则数据（统一视图）
const mockObjectRules = [
    {
        id: 1,
        type: 'meeting',
        value: '123456789',
        name: '',
        siteId: 1,
        siteName: '总部主站点',
        sitecode: '86-PXYLINK'
    },
    {
        id: 2,
        type: 'meeting',
        value: '987654321',
        name: '重要会议室规则',
        siteId: 1,
        siteName: '总部主站点',
        sitecode: '86-PXYLINK'
    },
    {
        id: 3,
        type: 'ip',
        value: '***********/24',
        name: '总部内部网络',
        siteId: 1,
        siteName: '总部主站点',
        sitecode: '86-PXYLINK'
    },
    {
        id: 4,
        type: 'department',
        value: '技术部',
        name: '',
        siteId: 1,
        siteName: '总部主站点',
        sitecode: '86-PXYLINK'
    },
    {
        id: 5,
        type: 'meeting',
        value: '555666777',
        name: '',
        siteId: 2,
        siteName: '郑州分站点',
        sitecode: '87-PXYLINK'
    },
    {
        id: 6,
        type: 'department',
        value: '郑州分公司',
        name: '分公司部门规则',
        siteId: 2,
        siteName: '郑州分站点',
        sitecode: '87-PXYLINK'
    },
    {
        id: 7,
        type: 'ip',
        value: '10.0.0.0/8',
        name: '',
        siteId: 4,
        siteName: '西安备份站点',
        sitecode: '89-PXYWEST'
    },
    {
        id: 8,
        type: 'meeting',
        value: 'SDK会议室',
        name: 'SDK会议室类型规则',
        siteId: 1,
        siteName: '总部主站点',
        sitecode: '86-PXYLINK'
    }
];

// 全局变量
let currentPage = 1;
const pageSize = 10;
let filteredRules = [...mockObjectRules];

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    loadFilters();
    loadRulesData();
    bindEvents();

    // 检查URL参数，如果有siteId则预选站点
    const urlParams = new URLSearchParams(window.location.search);
    const siteId = urlParams.get('siteId');
    if (siteId) {
        const siteFilter = document.getElementById('siteFilter');
        if (siteFilter) {
            siteFilter.value = siteId;
            handleFilter();
        }
    }
});

function initializePage() {
    // 移动端菜单切换
    const menuToggle = document.getElementById('menu-toggle');
    const sidebar = document.getElementById('sidebar');

    if (menuToggle && sidebar) {
        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('-translate-x-full');
        });
    }
}

function bindEvents() {
    // 搜索框事件
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', Utils.debounce(handleSearch, 300));
    }

    // 筛选器事件
    const filters = ['typeFilter', 'siteFilter'];
    filters.forEach(filterId => {
        const filterElement = document.getElementById(filterId);
        if (filterElement) {
            filterElement.addEventListener('change', handleFilter);
        }
    });

    // 表单提交事件
    const addRuleForm = document.getElementById('addRuleForm');
    if (addRuleForm) {
        addRuleForm.addEventListener('submit', handleAddRule);
    }
}

function loadFilters() {
    // 加载站点筛选器
    const siteFilter = document.getElementById('siteFilter');
    if (siteFilter) {
        siteFilter.innerHTML = '<option value="">所有站点</option>' +
            mockSites.map(site => `
                <option value="${site.id}">${site.siteName} (${site.sitecode})</option>
            `).join('');
    }

    // 加载站点选择器（新增规则表单）
    const siteSelector = document.querySelector('#addRuleForm select[name="siteId"]');
    if (siteSelector) {
        siteSelector.innerHTML = '<option value="">请选择站点</option>' +
            mockSites.map(site => `
                <option value="${site.id}">${site.siteName} (${site.sitecode})</option>
            `).join('');
    }
}

function loadRulesData() {
    const tbody = document.getElementById('rulesTableBody');
    if (!tbody) return;

    // 应用筛选
    applyFilters();

    // 分页
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageData = filteredRules.slice(startIndex, endIndex);

    // 渲染表格
    tbody.innerHTML = pageData.map(rule => `
        <tr>
            <td>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 rounded-full ${getTypeColor(rule.type)}"></div>
                    <span class="font-medium">${getTypeText(rule.type)}</span>
                </div>
            </td>
            <td>
                ${rule.name ? `<div class="font-medium text-gray-900">${rule.name}</div>` : ''}
            </td>
            <td>
                ${rule.type === 'meeting' && mockMeetings[rule.value] && mockMeetings[rule.value] !== rule.value ? `<div class="font-medium text-gray-900">${mockMeetings[rule.value]}</div>` : ''}
                ${rule.type === 'meeting' && mockMeetings[rule.value] && mockMeetings[rule.value] !== rule.value ? `<div class="text-sm text-gray-500">会议号：${rule.value}</div>` : `<div class="font-medium text-gray-900">${rule.value}</div>`}
            </td>
            <td>
                <div class="font-medium text-gray-900">${rule.siteName}</div>
                <div class="text-sm text-gray-500">${rule.sitecode}</div>
            </td>
            <td>
                <div class="flex items-center space-x-2">
                    <button class="btn-text btn-sm" onclick="editRule(${rule.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-text btn-sm text-red-600" onclick="deleteRule(${rule.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');

    // 更新分页信息
    updatePagination();
}

function applyFilters() {
    const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
    const typeFilter = document.getElementById('typeFilter')?.value || '';
    const siteFilter = document.getElementById('siteFilter')?.value || '';

    filteredRules = mockObjectRules.filter(rule => {
        const matchesSearch = !searchTerm ||
            rule.value.toLowerCase().includes(searchTerm) ||
            rule.siteName.toLowerCase().includes(searchTerm);

        const matchesType = !typeFilter || rule.type === typeFilter;
        const matchesSite = !siteFilter || rule.siteId == siteFilter;

        return matchesSearch && matchesType && matchesSite;
    });

    // 重置到第一页
    currentPage = 1;
}

function updatePagination() {
    const totalCount = filteredRules.length;
    const totalPages = Math.ceil(totalCount / pageSize);
    const startIndex = (currentPage - 1) * pageSize + 1;
    const endIndex = Math.min(currentPage * pageSize, totalCount);

    // 更新分页信息
    document.getElementById('pageStart').textContent = totalCount > 0 ? startIndex : 0;
    document.getElementById('pageEnd').textContent = endIndex;
    document.getElementById('totalCount').textContent = totalCount;

    // 更新分页按钮状态
    const prevBtn = document.getElementById('prevPage');
    const nextBtn = document.getElementById('nextPage');

    if (prevBtn) {
        prevBtn.classList.toggle('disabled', currentPage <= 1);
    }

    if (nextBtn) {
        nextBtn.classList.toggle('disabled', currentPage >= totalPages);
    }

    // 生成页码按钮
    const pageNumbers = document.getElementById('pageNumbers');
    if (pageNumbers) {
        const pages = [];
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            pages.push(`
                <button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="goToPage(${i})">
                    ${i}
                </button>
            `);
        }

        pageNumbers.innerHTML = pages.join('');
    }
}

function handleSearch() {
    loadRulesData();
}

function handleFilter() {
    loadRulesData();
}

function changePage(direction) {
    const totalPages = Math.ceil(filteredRules.length / pageSize);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        loadRulesData();
    }
}

function goToPage(page) {
    currentPage = page;
    loadRulesData();
}

// 刷新功能已移除

// 规则管理
function showAddRuleModal() {
    document.getElementById('addRuleModal').classList.remove('hidden');
}

function hideAddRuleModal() {
    document.getElementById('addRuleModal').classList.add('hidden');
    document.getElementById('addRuleForm').reset();
    document.getElementById('ruleFormContent').innerHTML = '';
}

function updateRuleForm() {
    const ruleType = document.getElementById('ruleTypeSelect').value;
    const formContent = document.getElementById('ruleFormContent');
    const ruleTypeHint = document.getElementById('ruleTypeHint');

    if (!ruleType) {
        formContent.innerHTML = '';
        ruleTypeHint.classList.add('hidden');
        return;
    }
    
    // 显示规则类型提示
    let hintText = '';
    switch (ruleType) {
        case 'meeting':
            hintText = '接入该会议室的终端/用户通过会议室将分配的站点进行呼叫、录制';
            break;
        case 'ip':
            hintText = '使用该IP的终端/用户将通过IP分配的站点进行呼叫、点播';
            break;
        case 'department':
            hintText = '该部门及其所有下属部门中的终端/用户将通过部门分配的站点进行呼叫、点播';
            break;
    }
    ruleTypeHint.textContent = hintText;
    ruleTypeHint.classList.remove('hidden');

    let formHTML = '';

    switch (ruleType) {
        case 'meeting':
            formHTML = `
                <div class="form-group">
                    <label class="form-label required">选择方式</label>
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="radio" name="meetingSelectionType" value="type" class="mr-2" onchange="toggleMeetingSelectionType()" checked>
                            <span>会议室类型</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="meetingSelectionType" value="single" class="mr-2" onchange="toggleMeetingSelectionType()">
                            <span>单个会议室</span>
                        </label>
                    </div>
                </div>

                <div id="meetingTypeSelection" class="form-group">
                    <label class="form-label required">会议室类型</label>
                    <select class="form-control" name="meetingType" required>
                        <option value="">请选择会议室类型</option>
                        <option value="个人会议室">个人会议室</option>
                        <option value="企业会议室">企业会议室</option>
                        <option value="企业随机会议室">企业随机会议室</option>
                        <option value="SDK会议室">SDK会议室</option>
                        <option value="群组会议室">群组会议室</option>
                    </select>
                </div>

                <div id="singleMeetingSelection" class="form-group hidden">
                    <label class="form-label required">会议室</label>
                    <div class="relative">
                        <input type="text" class="form-control" name="ruleValue" placeholder="输入会议室进行搜索..."
                               oninput="searchMeetings(this.value)" autocomplete="off">
                        <div class="absolute right-2 top-2">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <div class="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-b-lg shadow-lg z-10 hidden" id="meetingSearchResults">
                            <!-- 搜索结果 -->
                        </div>
                    </div>
                    <div class="text-sm text-gray-500 mt-1">仅支持个人/企业会议室</div>
                </div>

                <div class="form-group">
                    <label class="form-label">规则名称</label>
                    <input type="text" class="form-control" name="meetingName" placeholder="规则名称（可选）">
                </div>
            `;
            break;
        case 'ip':
            formHTML = `
                <div class="form-group">
                    <label class="form-label required">终端/代理IP</label>
                    <input type="text" class="form-control" name="ruleValue" placeholder="如：************* 或 ***********/24" required>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-3">
                        <h5 class="font-medium text-blue-800 mb-2">地址格式说明：</h5>
                        <div class="text-sm text-blue-700 space-y-1">
                            <div><strong>IPv4:</strong> *******[/24], 1.2-4.3-5.4[/24]</div>
                            <div><strong>IPv6:</strong> 1:2:3:4::[/24], 1:2-4:3-5:4::[/24]</div>
                            <div class="text-blue-600">IPv6段禁止0开头 (例: 1:1:1::而非0001:0001:0001::)</div>
                        </div>
                        <h5 class="font-medium text-blue-800 mb-2 mt-3">生效时间：</h5>
                        <div class="text-sm text-blue-700 space-y-1">
                            <div><strong>IP段:</strong> 10分钟</div>
                            <div><strong>IP:</strong> 立即</div>
                        </div>
                        <h5 class="font-medium text-blue-800 mb-2 mt-3">注意事项：</h5>
                        <div class="text-sm text-blue-700 space-y-1">
                            <div><strong>范围地址重复:</strong> 添加"-"格式地址范围不校验重复</div>
                            <div>例: *******=86 和 1.2-4.3.4=87 都含 *******</div>
                            <div><strong>系统遵循最长匹配:</strong></div>
                            <div>例: *******/24=86 和 *******/32=87，******* 匹配 *******/32=87</div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">规则名称</label>
                    <input type="text" class="form-control" name="ipRuleName" placeholder="如：总部网段、开发环境网段（可选）">
                </div>
            `;
            break;
        case 'department':
            formHTML = `
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-blue-600 mr-2 mt-0.5"></i>
                        <div class="text-sm text-blue-800">
                            <div class="font-medium mb-1">性能提示</div>
                            <div>按部门就近接入会影响服务性能。如非必要，请优先考虑其他方案。</div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label required">部门</label>
                    <input type="text" class="form-control" name="ruleVa lue" placeholder="如：技术部 或 技术部/开发组" required>                </div>
                <div class="form-group">
                    <label class="form-label">规则名称</label>
                    <input type="text" class="form-control" name="departmentRuleName" placeholder="如：技术部门规则、开发组规则（可选）">
                </div>
            `;
            break;
    }

    formContent.innerHTML = formHTML;
}

// 会议室搜索功能
function searchMeetings(query) {
    const resultsContainer = document.getElementById('meetingSearchResults');
    if (!resultsContainer) return;

    if (!query || query.length < 2) {
        resultsContainer.classList.add('hidden');
        return;
    }

    // 搜索匹配的会议室
    const matches = Object.entries(mockMeetings).filter(([meetingId, meetingName]) =>
        meetingId.includes(query) || meetingName.toLowerCase().includes(query.toLowerCase())
    );

    if (matches.length === 0) {
        resultsContainer.classList.add('hidden');
        return;
    }

    resultsContainer.innerHTML = matches.map(([meetingId, meetingName]) => `
        <div class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
             onclick="selectMeeting('${meetingId}', '${meetingName}')">
            <div class="font-medium text-gray-900">${meetingName}</div>
            <div class="text-sm text-gray-500">会议室: ${meetingId}</div>
        </div>
    `).join('');

    resultsContainer.classList.remove('hidden');
}

function selectMeeting(meetingId, meetingName) {
    const ruleValueInput = document.querySelector('input[name="ruleValue"]');
    const meetingNameInput = document.querySelector('input[name="meetingName"]');
    const resultsContainer = document.getElementById('meetingSearchResults');

    if (ruleValueInput) ruleValueInput.value = meetingId;
    if (meetingNameInput) meetingNameInput.value = meetingName;
    if (resultsContainer) resultsContainer.classList.add('hidden');
}

// 切换会议室选择类型
function toggleMeetingSelectionType() {
    const selectionType = document.querySelector('input[name="meetingSelectionType"]:checked')?.value;
    const typeSelection = document.getElementById('meetingTypeSelection');
    const singleSelection = document.getElementById('singleMeetingSelection');

    if (selectionType === 'type') {
        typeSelection?.classList.remove('hidden');
        singleSelection?.classList.add('hidden');

        // 清空单个会议室的输入
        const ruleValueInput = document.querySelector('input[name="ruleValue"]');
        if (ruleValueInput) ruleValueInput.value = '';

        // 设置会议室类型为必填
        const meetingTypeSelect = document.querySelector('select[name="meetingType"]');
        if (meetingTypeSelect) meetingTypeSelect.required = true;
    } else if (selectionType === 'single') {
        typeSelection?.classList.add('hidden');
        singleSelection?.classList.remove('hidden');

        // 清空会议室类型选择
        const meetingTypeSelect = document.querySelector('select[name="meetingType"]');
        if (meetingTypeSelect) {
            meetingTypeSelect.value = '';
            meetingTypeSelect.required = false;
        }

        // 设置单个会议室为必填
        const ruleValueInput = document.querySelector('input[name="ruleValue"]');
        if (ruleValueInput) ruleValueInput.required = true;
    }
}

// 点击外部关闭搜索结果
document.addEventListener('click', function(e) {
    const resultsContainer = document.getElementById('meetingSearchResults');
    const searchInput = document.querySelector('input[name="ruleValue"]');

    if (resultsContainer && searchInput &&
        !resultsContainer.contains(e.target) &&
        !searchInput.contains(e.target)) {
        resultsContainer.classList.add('hidden');
    }
});

function handleAddRule(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const ruleType = formData.get('ruleType');
    const meetingSelectionType = formData.get('meetingSelectionType');
    const meetingType = formData.get('meetingType');
    const ruleValue = formData.get('ruleValue');
    const meetingName = formData.get('meetingName');
    const ipRuleName = formData.get('ipRuleName');
    const departmentRuleName = formData.get('departmentRuleName');
    const siteId = parseInt(formData.get('siteId'));

    // 验证必填字段
    let actualRuleValue = ruleValue;
    if (ruleType === 'meeting') {
        if (meetingSelectionType === 'type') {
            if (!meetingType) {
                Message.error('请选择会议室类型');
                return;
            }
            actualRuleValue = meetingType;
        } else if (meetingSelectionType === 'single') {
            if (!ruleValue) {
                Message.error('请选择会议室');
                return;
            }
        }
    }

    if (!ruleType || !actualRuleValue || !siteId) {
        Message.error('请填写完整的规则信息');
        return;
    }

    // 查找站点信息
    const site = mockSites.find(s => s.id === siteId);
    if (!site) {
        Message.error('选择的站点不存在');
        return;
    }



    // 验证站点能力
    const capabilityCheck = checkSiteCapabilityForRule(site, ruleType);
    if (!capabilityCheck.hasCapability) {
        Message.error(capabilityCheck.message);
        return;
    }

    // 检查是否存在相同的规则
    const existingRule = mockObjectRules.find(rule =>
        rule.type === ruleType &&
        rule.value === actualRuleValue &&
        rule.siteId === siteId
    );

    if (existingRule) {
        Message.error('相同的规则已存在');
        return;
    }

    // 创建新规则
    let ruleName;
    switch (ruleType) {
        case 'meeting':
            if (meetingSelectionType === 'type') {
                // 会议室类型的显示名称
                ruleName = meetingName || meetingType;
            } else {
                // 单个会议室的显示名称
                ruleName = meetingName || mockMeetings[actualRuleValue] || actualRuleValue;
            }
            break;
        case 'ip':
            ruleName = ipRuleName || '';
            break;
        case 'department':
            ruleName = departmentRuleName || '';
            break;
        default:
            ruleName = '';
    }

    const newRule = {
        id: Date.now(),
        type: ruleType,
        value: actualRuleValue,
        name: ruleName,
        siteId: siteId,
        siteName: site.siteName,
        sitecode: site.sitecode
    };

    // 添加规则
    mockObjectRules.push(newRule);

    // 刷新显示
    loadRulesData();
    hideAddRuleModal();

    Message.success('规则添加成功');
}

// 获取规则类型所需的能力
function getRuleRequiredCapabilities(ruleType) {
    switch (ruleType) {
        case 'meeting':
            return ['call', 'recording', 'vod']; // 会议号规则需要呼叫、录制和点播能力
        case 'ip':
            return ['call']; // IP规则主要用于呼叫路由
        case 'department':
            return ['call']; // 部门规则主要用于呼叫路由
        default:
            return [];
    }
}

// 检查站点是否具备处理规则的完整能力（包括备份站点）
function checkSiteCapabilityForRule(site, ruleType) {
    const requiredCapabilities = getRuleRequiredCapabilities(ruleType);
    const missingCapabilities = requiredCapabilities.filter(cap => !site.capabilities.includes(cap));

    if (missingCapabilities.length === 0) {
        return { hasCapability: true, message: '' };
    }

    // 检查备份站点能力
    const backupSites = site.backupSites || { call: [], recording: [], vod: [] };
    const missingAfterBackup = missingCapabilities.filter(cap => {
        const backupSiteList = backupSites[cap] || [];
        return !backupSiteList.some(backupSite => {
            const backupSiteCode = typeof backupSite === 'string' ? backupSite : backupSite.sitecode;
            const actualBackupSite = mockSites.find(s => s.sitecode === backupSiteCode);
            return actualBackupSite && actualBackupSite.capabilities.includes(cap);
        });
    });

    if (missingAfterBackup.length === 0) {
        return { hasCapability: true, message: '' };
    }

    // 生成详细的错误信息
    const capabilityNames = missingAfterBackup.map(cap => {
        switch(cap) {
            case 'call': return '呼叫';
            case 'recording': return '录制';
            case 'vod': return '点播';
            default: return cap;
        }
    }).join('、');
    let message = '';

    if (site.siteName === '武汉测试站点') {
        if (ruleType === 'meeting') {
            message = `武汉测试站点仅支持录制和点播服务，无法处理会议号规则（需要呼叫+录制+点播能力）。\n\n建议解决方案：\n1. 为该站点添加具备呼叫能力的备份站点\n2. 推荐备份站点：总部主站点、郑州分站点、西安备份站点`;
        } else if (ruleType === 'ip') {
            message = `武汉测试站点仅支持录制和点播服务，无法处理IP规则（需要呼叫能力进行路由）。\n\n建议解决方案：\n1. 为该站点添加具备呼叫能力的备份站点\n2. 推荐备份站点：总部主站点、郑州分站点、西安备份站点`;
        } else if (ruleType === 'department') {
            message = `武汉测试站点仅支持录制和点播服务，无法处理部门规则（需要呼叫能力进行路由）。\n\n建议解决方案：\n1. 为该站点添加具备呼叫能力的备份站点\n2. 推荐备份站点：总部主站点、郑州分站点、西安备份站点`;
        }
    } else {
        message = `站点"${site.siteName}"缺少${capabilityNames}服务能力。\n\n请为该站点添加具备${capabilityNames}能力的备份站点。`;
    }

    return { hasCapability: false, message };
}

// 对象规则页面的站点能力检查
function checkSiteCapabilityInObjectRules() {
    const warningDiv = document.getElementById('siteCapabilityWarningObjectRules');
    const warningText = document.getElementById('warningTextObjectRules');

    if (!warningDiv || !warningText) return;

    // 获取当前选择的站点
    const siteSelect = document.querySelector('select[name="siteId"]');
    const siteId = siteSelect ? parseInt(siteSelect.value) : null;

    // 如果选择了站点，检查站点能力
    if (siteId) {
        const site = mockSites.find(s => s.id === siteId);
        if (site) {
            // 检查站点是否缺少任何服务能力
            const allCapabilities = ['call', 'recording', 'vod'];
            const missingCapabilities = allCapabilities.filter(cap => !site.capabilities.includes(cap));

            if (missingCapabilities.length > 0) {
                // 检查备份站点是否能提供缺失的能力
                const backupSites = site.backupSites || { call: [], recording: [], vod: [] };
                const stillMissing = missingCapabilities.filter(cap => {
                    const backupSiteList = backupSites[cap] || [];
                    return !backupSiteList.some(backupSite => {
                        const backupSiteCode = typeof backupSite === 'string' ? backupSite : backupSite.sitecode;
                        const actualBackupSite = mockSites.find(s => s.sitecode === backupSiteCode);
                        return actualBackupSite && actualBackupSite.capabilities.includes(cap);
                    });
                });

                if (stillMissing.length > 0) {
                    const capabilityNames = stillMissing.map(cap => {
                        switch(cap) {
                            case 'call': return '呼叫';
                            case 'recording': return '录制';
                            case 'vod': return '点播';
                            default: return cap;
                        }
                    }).join('、');
                    warningDiv.classList.remove('hidden');
                    warningText.textContent = `站点"${site.siteName}"缺少${capabilityNames}服务能力，请为该站点添加具备${capabilityNames}能力的备份站点。如已添加请忽略。`;
                } else {
                    warningDiv.classList.add('hidden');
                }
            } else {
                warningDiv.classList.add('hidden');
            }
        }
    } else {
        warningDiv.classList.add('hidden');
    }
}

function editRule(ruleId) {
    Message.info('编辑功能开发中...');
}


function deleteRule(ruleId) {
    const rule = mockObjectRules.find(r => r.id === ruleId);
    if (!rule) {
        Message.error('规则不存在');
        return;
    }

    Confirm.show(
        `确定要删除规则 "${rule.value}" 吗？此操作不可撤销。`,
        '确认删除',
        () => {
            const ruleIndex = mockObjectRules.findIndex(r => r.id === ruleId);
            if (ruleIndex > -1) {
                mockObjectRules.splice(ruleIndex, 1);
                loadRulesData();
                Message.success('规则删除成功');
            }
        }
    );
}

// 批量导入
function showBatchImportModal() {
    document.getElementById('batchImportModal').classList.remove('hidden');
}

function hideBatchImportModal() {
    document.getElementById('batchImportModal').classList.add('hidden');
    document.getElementById('importFile').value = '';
}

function downloadTemplate() {
    Message.info('模板下载功能开发中...');
}

function handleBatchImport() {
    Message.info('批量导入功能开发中...');
}

// 工具函数
function getTypeColor(type) {
    const colors = {
        'meeting': 'bg-red-500',
        'ip': 'bg-yellow-500',
        'department': 'bg-blue-500'
    };
    return colors[type] || 'bg-gray-500';
}

function getTypeText(type) {
    const texts = {
        'meeting': '会议室',
        'ip': '终端/代理IP',
        'department': '部门'
    };
    return texts[type] || type;
}
