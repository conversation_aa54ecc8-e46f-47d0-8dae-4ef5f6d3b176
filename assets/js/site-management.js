// 站点管理页面JavaScript

// 模拟数据
const mockSites = [
    {
        id: 1,
        sitecode: '86-PXYLINK',
        siteName: '总部主站点',
        region: '总部',
        network: '内网',
        capabilities: ['call', 'recording', 'vod'],
        serverCount: {
            call: { active: 4, total: 5 },
            recording: { active: 1, total: 2 },
            vod: { active: 1, total: 1 }
        },
        servers: {
            call: [
                { name: 'HQ-CALL-01', status: 'Running' },
                { name: 'HQ-CALL-02', status: 'Running' },
                { name: 'HQ-CALL-03', status: 'Running' },
                { name: 'HQ-CALL-04', status: 'Running' },
                { name: 'HQ-CALL-05', status: 'Failed' }
            ],
            recording: [
                { name: 'HQ-RECORDING-01', status: 'Running' },
                { name: 'HQ-RECORDING-02', status: 'Running' }
            ],
            vod: [
                { name: 'HQ-VOD-01', status: 'Running' }
            ]
        },
        backupSites: {
            call: [
                { sitecode: '87-PXYLINK', siteName: '郑州分站点' },
                { sitecode: '89-PXYWEST', siteName: '西安备份站点' }
            ],
            recording: [
                { sitecode: '88-PXYTEST', siteName: '武汉测试站点' },
                { sitecode: '90-PXYCLOUD', siteName: '云端站点' }
            ],
            vod: [
                { sitecode: '89-PXYWEST', siteName: '西安备份站点' }
            ]
        }
    },
    {
        id: 2,
        sitecode: '87-PXYLINK',
        siteName: '郑州分站点',
        region: '郑州',
        network: 'DMZ',
        capabilities: ['call'],
        serverCount: {
            call: { active: 3, total: 4 },
            recording: { active: 0, total: 0 },
            vod: { active: 0, total: 0 }
        },
        servers: {
            call: [
                { name: 'ZZ-CALL-01', status: 'Running' },
                { name: 'ZZ-CALL-02', status: 'Running' },
                { name: 'ZZ-CALL-03', status: 'Running' },
                { name: 'ZZ-CALL-04', status: 'Failed' }
            ],
            recording: [],
            vod: []
        },
        backupSites: {
            call: [
                { sitecode: '86-PXYLINK', siteName: '总部主站点' }
            ],
            recording: [],
            vod: []
        }
    },
    {
        id: 3,
        sitecode: '88-PXYTEST',
        siteName: '武汉测试站点',
        region: '武汉',
        network: '互联网',
        capabilities: ['recording', 'vod'],
        serverCount: {
            call: { active: 0, total: 0 },
            recording: { active: 1, total: 1 },
            vod: { active: 1, total: 1 }
        },
        servers: {
            call: [],
            recording: [
                { name: 'WH-RECORDING-01', status: 'Failed' }
            ],
            vod: [
                { name: 'WH-VOD-01', status: 'Running' }
            ]
        },
        backupSites: {
            call: [],
            recording: [
                { sitecode: '90-PXYCLOUD', siteName: '云端站点' }
            ],
            vod: [
                { sitecode: '86-PXYLINK', siteName: '总部主站点' }
            ]
        }
    },
    {
        id: 4,
        sitecode: '89-PXYWEST',
        siteName: '西安备份站点',
        region: '西安',
        network: '内网',
        capabilities: ['call', 'recording', 'vod'],
        serverCount: {
            call: { active: 2, total: 3 },
            recording: { active: 2, total: 2 },
            vod: { active: 1, total: 1 }
        },
        servers: {
            call: [
                { name: 'XA-CALL-01', status: 'Running' },
                { name: 'XA-CALL-02', status: 'Running' },
                { name: 'XA-CALL-03', status: 'Failed' }
            ],
            recording: [
                { name: 'XA-RECORDING-01', status: 'Running' },
                { name: 'XA-RECORDING-02', status: 'Running' }
            ],
            vod: [
                { name: 'XA-VOD-01', status: 'Running' }
            ]
        },
        backupSites: {
            call: [
                { sitecode: '86-PXYLINK', siteName: '总部主站点' },
                { sitecode: '90-PXYCLOUD', siteName: '云端站点' }
            ],
            recording: [
                { sitecode: '86-PXYLINK', siteName: '总部主站点' }
            ],
            vod: [
                { sitecode: '90-PXYCLOUD', siteName: '云端站点' }
            ]
        }
    },
    {
        id: 5,
        sitecode: '90-PXYCLOUD',
        siteName: '云端站点',
        region: '总部',
        network: '互联网',
        capabilities: ['call', 'recording', 'vod'],
        serverCount: {
            call: { active: 6, total: 8 },
            recording: { active: 2, total: 2 },
            vod: { active: 2, total: 2 }
        },
        servers: {
            call: [
                { name: 'CLOUD-CALL-01', status: 'Running' },
                { name: 'CLOUD-CALL-02', status: 'Running' },
                { name: 'CLOUD-CALL-03', status: 'Running' },
                { name: 'CLOUD-CALL-04', status: 'Running' },
                { name: 'CLOUD-CALL-05', status: 'Running' },
                { name: 'CLOUD-CALL-06', status: 'Running' },
                { name: 'CLOUD-CALL-07', status: 'Failed' },
                { name: 'CLOUD-CALL-08', status: 'Failed' }
            ],
            recording: [
                { name: 'CLOUD-RECORDING-01', status: 'Running' },
                { name: 'CLOUD-RECORDING-02', status: 'Running' }
            ],
            vod: [
                { name: 'CLOUD-VOD-01', status: 'Running' },
                { name: 'CLOUD-VOD-02', status: 'Running' }
            ]
        },
        backupSites: {
            call: [],
            recording: [],
            vod: []
        }
    }
];

// 模拟规则数据
const mockRules = {
    1: [
        { type: 'meeting', value: '123456789', name: 'VIP会议室' },
        { type: 'ip', value: '***********/24', name: '总部网段' },
        { type: 'department', value: '技术部', name: '技术部' }
    ],
    2: [
        { type: 'meeting', value: '555666777', name: '郑州会议室' },
        { type: 'department', value: '郑州分公司', name: '郑州分公司' }
    ],
    3: [
        { type: 'meeting', value: '888999000', name: '测试会议室' }
    ],
    4: [
        { type: 'ip', value: '10.0.0.0/8', name: '内网大段' }
    ],
    5: []
};

// 全局变量
let currentPage = 1;
const pageSize = 10;
let filteredSites = [...mockSites];
let expandedSites = new Set(); // 跟踪展开的站点
let currentEditingSite = null;


// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    bindEvents();
    loadSiteData();
    initializeTooltips();
    loadRegionAndNetworkOptions(); // 加载区域和网络选项
});

// 加载区域和网络选项
function loadRegionAndNetworkOptions() {
    // 检查是否有元数据管理的数据源
    if (typeof getRegions === 'function' && typeof getNetworks === 'function') {
        const regions = getRegions();
        const networks = getNetworks();

        // 更新筛选器选项
        updateFilterOptions(regions, networks);

        // 更新新增站点表单选项
        updateAddSiteFormOptions(regions, networks);
    } else {
        console.warn('元数据管理数据源不可用，使用默认选项');
    }
}

// 更新筛选器选项
function updateFilterOptions(regions, networks) {
    const regionFilter = document.getElementById('regionFilter');
    const networkFilter = document.getElementById('networkFilter');

    if (regionFilter) {
        regionFilter.innerHTML = '<option value="">所有区域</option>' +
            regions.map(region => `<option value="${region.name}">${region.name}</option>`).join('');
    }

    if (networkFilter) {
        networkFilter.innerHTML = '<option value="">所有网络</option>' +
            networks.map(network => `<option value="${network.name}">${network.name}</option>`).join('');
    }
}

// 更新新增站点表单选项
function updateAddSiteFormOptions(regions, networks) {
    const regionSelect = document.querySelector('#addSiteForm select[name="region"]');
    const networkSelect = document.querySelector('#addSiteForm select[name="network"]');

    if (regionSelect) {
        regionSelect.innerHTML = '<option value="">请选择区域</option>' +
            regions.map(region => `<option value="${region.name}">${region.name}</option>`).join('');
    }

    if (networkSelect) {
        networkSelect.innerHTML = '<option value="">请选择网络</option>' +
            networks.map(network => `<option value="${network.name}">${network.name}</option>`).join('');
    }
}

// 初始化自定义tooltips
function initializeTooltips() {
    document.querySelectorAll('.tooltip').forEach(tooltip => {
        const icon = tooltip.querySelector('.tooltip-icon');
        const tooltipText = tooltip.querySelector('.tooltip-text');
        const tooltipArrow = tooltip.querySelector('.tooltip-arrow');

        icon.addEventListener('mouseenter', (e) => {
            const iconRect = icon.getBoundingClientRect();
            const tooltipRect = tooltipText.getBoundingClientRect();

            // 计算tooltip位置
            const top = iconRect.top - tooltipRect.height - 15;
            const left = iconRect.left + (iconRect.width / 2) - (tooltipRect.width / 2);

            // 设置tooltip位置
            tooltipText.style.top = `${Math.max(8, top)}px`;
            tooltipText.style.left = `${Math.max(8, Math.min(left, window.innerWidth - tooltipRect.width - 8))}px`;

            // 设置箭头位置
            const arrowLeft = iconRect.left + (iconRect.width / 2) - 6;
            tooltipArrow.style.top = `${iconRect.top - 12}px`;
            tooltipArrow.style.left = `${arrowLeft}px`;
        });
    });
}

function initializePage() {
    // 移动端菜单切换
    const menuToggle = document.getElementById('menu-toggle');
    const sidebar = document.getElementById('sidebar');

    if (menuToggle && sidebar) {
        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('-translate-x-full');
        });
    }
}

function bindEvents() {
    // 搜索框事件
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', Utils.debounce(handleSearch, 300));
    }

    // 筛选器事件
    const filters = ['regionFilter', 'networkFilter'];
    filters.forEach(filterId => {
        const filterElement = document.getElementById(filterId);
        if (filterElement) {
            filterElement.addEventListener('change', handleFilter);
        }
    });

    // 表单提交事件
    const addSiteForm = document.getElementById('addSiteForm');
    if (addSiteForm) {
        addSiteForm.addEventListener('submit', handleAddSite);
    }
}

function loadSiteData() {
    const tbody = document.getElementById('siteTableBody');
    if (!tbody) return;

    // 应用筛选
    applyFilters();

    // 分页
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageData = filteredSites.slice(startIndex, endIndex);

    // 渲染表格
    tbody.innerHTML = pageData.map(site => `
        <tr class="site-row" data-site-id="${site.id}">
            <td>
                <button class="expand-btn" onclick="event.stopPropagation(); toggleSiteExpansion(${site.id})" title="展开/收起详情">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </td>
            <td onclick="toggleSiteExpansion(${site.id})">
                <div class="font-medium text-gray-900">${site.siteName}</div>
                <div class="text-sm text-gray-400">${site.sitecode}</div>
            </td>
            <td onclick="toggleSiteExpansion(${site.id})">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <i class="fas fa-map-marker-alt mr-1"></i>
                    ${site.region}
                </span>
            </td>
            <td onclick="toggleSiteExpansion(${site.id})">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getNetworkClass(site.network)}">
                    <i class="fas fa-network-wired mr-1"></i>
                    ${site.network}
                </span>
            </td>
            <td onclick="toggleSiteExpansion(${site.id})">
                <div class="text-sm">
                    ${formatServiceCount(site.serverCount)}
                </div>
            </td>
            <td onclick="toggleSiteExpansion(${site.id})">
                <div class="text-sm">
                    ${formatBackupSites(site.backupSites)}
                </div>
            </td>
            <td onclick="toggleSiteExpansion(${site.id})">
                <div class="text-sm">
                    ${formatRules(mockRules[site.id] || [])}
                </div>
            </td>
            <td onclick="event.stopPropagation()">
                <div class="flex items-center space-x-2">
                    <button class="btn-text btn-sm" onclick="showSitePanel(${site.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    ${site.sitecode !== '86-PXYLINK' && !mockRules[site.id]?.length && !(site.serverCount.call.total > 0 || site.serverCount.recording.total > 0 || site.serverCount.vod.total > 0) ?
                        `<button class="btn-text btn-sm text-red-600" onclick="deleteSite(${site.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>` :
                        site.sitecode !== '86-PXYLINK' ?
                        `<button class="btn-text btn-sm text-gray-400" onclick="showCannotDeleteInfo(${site.id})" title="无法删除">
                            <i class="fas fa-trash"></i>
                        </button>` :
                        ''}
                </div>
            </td>
        </tr>
        <tr class="expanded-content" id="expanded-${site.id}">
            <td colspan="8">
                <div class="expanded-inner">
                    <div class="tab-container">
                        <div class="tab-nav">
                            <button class="tab-nav-item active" onclick="switchTab(${site.id}, 'server')">
                                <i class="fas fa-server mr-2"></i>服务信息
                            </button>
                            <button class="tab-nav-item" onclick="switchTab(${site.id}, 'backup')">
                                <i class="fas fa-shield-alt mr-2"></i>备份站点
                            </button>
                            <button class="tab-nav-item" onclick="switchTab(${site.id}, 'rules')">
                                <i class="fas fa-cogs mr-2"></i>接入规则
                            </button>
                        </div>
                        <div class="tab-content">
                            <div class="tab-pane active" id="tab-server-${site.id}">
                                <!-- 服务信息内容将通过JavaScript动态加载 -->
                            </div>
                            <div class="tab-pane" id="tab-backup-${site.id}">
                                <!-- 备份站点内容将通过JavaScript动态加载 -->
                            </div>
                            <div class="tab-pane" id="tab-rules-${site.id}">
                                <!-- 接入规则内容将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
    `).join('');

    // 更新分页信息
    updatePagination();
}

function applyFilters() {
    const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
    const regionFilter = document.getElementById('regionFilter')?.value || '';
    const networkFilter = document.getElementById('networkFilter')?.value || '';

    filteredSites = mockSites.filter(site => {
        const matchesSearch = !searchTerm ||
            site.sitecode.toLowerCase().includes(searchTerm) ||
            site.siteName.toLowerCase().includes(searchTerm) ||
            site.region.toLowerCase().includes(searchTerm);

        const matchesRegion = !regionFilter || site.region === regionFilter;
        const matchesNetwork = !networkFilter || site.network === networkFilter;

        return matchesSearch && matchesRegion && matchesNetwork;
    });

    // 重置到第一页
    currentPage = 1;
}

function updatePagination() {
    const totalCount = filteredSites.length;
    const totalPages = Math.ceil(totalCount / pageSize);
    const startIndex = (currentPage - 1) * pageSize + 1;
    const endIndex = Math.min(currentPage * pageSize, totalCount);

    // 更新分页信息
    document.getElementById('pageStart').textContent = totalCount > 0 ? startIndex : 0;
    document.getElementById('pageEnd').textContent = endIndex;
    document.getElementById('totalCount').textContent = totalCount;

    // 更新分页按钮状态
    const prevBtn = document.getElementById('prevPage');
    const nextBtn = document.getElementById('nextPage');

    if (prevBtn) {
        prevBtn.classList.toggle('disabled', currentPage <= 1);
    }

    if (nextBtn) {
        nextBtn.classList.toggle('disabled', currentPage >= totalPages);
    }

    // 生成页码按钮
    const pageNumbers = document.getElementById('pageNumbers');
    if (pageNumbers) {
        const pages = [];
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            pages.push(`
                <button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="goToPage(${i})">
                    ${i}
                </button>
            `);
        }

        pageNumbers.innerHTML = pages.join('');
    }
}

function handleSearch() {
    loadSiteData();
}

function handleFilter() {
    loadSiteData();
}

function changePage(direction) {
    const totalPages = Math.ceil(filteredSites.length / pageSize);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        loadSiteData();
    }
}

function goToPage(page) {
    currentPage = page;
    loadSiteData();
}

// 刷新功能已移除

function showAddSiteModal() {
    document.getElementById('addSiteModal').classList.remove('hidden');
}

function hideAddSiteModal() {
    document.getElementById('addSiteModal').classList.add('hidden');
    document.getElementById('addSiteForm').reset();
}

function handleAddSite(event) {
    event.preventDefault();

    const formData = new FormData(event.target);

    // 获取表单数据
    const siteCode = formData.get('siteCode').trim();
    const siteName = formData.get('siteName');
    const region = formData.get('region');
    const network = formData.get('network');

    // 如果站点编码为空，自动生成一个
    let finalSiteCode = siteCode;
    if (!finalSiteCode) {
        // 生成格式：2位随机数字 + PXYLINK
        const randomDigits = Math.floor(Math.random() * 90 + 10).toString(); // 生成10-99的随机数字
        finalSiteCode = `${randomDigits}PXYLINK`;
    }

    const newSite = {
        id: mockSites.length + 1,
        sitecode: finalSiteCode,
        siteName: siteName,
        region: region,
        network: network,
        capabilities: [],
        serverCount: {
            call: { active: 0, total: 0 },
            recording: { active: 0, total: 0 },
            vod: { active: 0, total: 0 }
        },
        servers: {
            call: [],
            recording: [],
            vod: []
        },
        backupSites: {
            call: [],
            recording: [],
            vod: []
        }
    };

    // 验证站点编码唯一性
    if (mockSites.some(site => site.sitecode === newSite.sitecode)) {
        Message.error('站点编码已存在，请使用其他站点编码');
        return;
    }

    // 验证站点名称唯一性
    if (mockSites.some(site => site.siteName === newSite.siteName)) {
        Message.error('站点名称已存在，请使用其他站点名称');
        return;
    }

    mockSites.push(newSite);
    hideAddSiteModal();
    loadSiteData();

    // 显示生成的站点编码信息
    if (!siteCode) {
        Message.success(`站点添加成功！系统自动生成的站点编码为：${finalSiteCode}`);
    } else {
        Message.success('站点添加成功');
    }
}

// 这些函数已被showSitePanel替代

function deleteSite(siteId) {
    const site = mockSites.find(s => s.id === siteId);
    if (!site) return;

    // 防止通过 JS 删除核心站点
    if (site.sitecode === '86-PXYLINK') {
        Message.error('核心站点不可删除');
        return;
    }

    // 检查接入规则和绑定服务 - 有规则或绑定服务的站点禁止删除
    const hasRules = mockRules[siteId] && mockRules[siteId].length > 0;
    const hasBoundServers = site.serverCount.call.total > 0 || site.serverCount.recording.total > 0 || site.serverCount.vod.total > 0;

    if (hasRules || hasBoundServers) {
        // 显示提示模态框
        showCannotDeleteModal(site, hasRules, hasBoundServers);
        return;
    }

    Confirm.show(
        `确定要删除站点 "${site.siteName}" 吗？此操作不可撤销。`,
        '确认删除',
        () => {
            const index = mockSites.findIndex(s => s.id === siteId);
            if (index > -1) {
                mockSites.splice(index, 1);
                loadSiteData();
                Message.success('站点删除成功');
            }
        }
    );
}

// 工具函数
function getNetworkClass(network) {
    const classes = {
        '内网': 'bg-green-100 text-green-800',
        'DMZ': 'bg-yellow-100 text-yellow-800',
        '互联网': 'bg-red-100 text-red-800'
    };
    return classes[network] || 'bg-gray-100 text-gray-800';
}

function getServerStatusClass(status) {
    const classes = {
        'Running': 'bg-green-100 text-green-800',
        'Failed': 'bg-red-100 text-red-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
}

function getServerStatusIcon(status) {
    const icons = {
        'Running': 'fa-check-circle',
        'Failed': 'fa-times-circle'
    };
    return icons[status] || 'fa-question-circle';
}

function formatServiceCount(serverCount) {
    let html = '';
    if (serverCount.call.total > 0) {
        html += `<div class="flex items-center mb-1">
            <i class="fas fa-phone text-blue-600 mr-1"></i>
            <span class="text-gray-500">呼叫</span>
            <span class="text-blue-600 font-medium ml-1">${serverCount.call.active}/${serverCount.call.total}个</span>
        </div>`;
    }
    if (serverCount.recording.total > 0) {
        html += `<div class="flex items-center mb-1">
            <i class="fas fa-record-vinyl text-green-600 mr-1"></i>
            <span class="text-gray-500">录制</span>
            <span class="text-green-600 font-medium ml-1">${serverCount.recording.active}/${serverCount.recording.total}个</span>
        </div>`;
    }
    if (serverCount.vod.total > 0) {
        html += `<div class="flex items-center">
            <i class="fas fa-play-circle text-orange-600 mr-1"></i>
            <span class="text-gray-500">点播</span>
            <span class="text-orange-600 font-medium ml-1">${serverCount.vod.active}/${serverCount.vod.total}个</span>
        </div>`;
    }
    if (serverCount.call.total === 0 && serverCount.recording.total === 0 && serverCount.vod.total === 0) {
        html = '<span class="text-gray-400">无服务</span>';
    }
    return html;
}

function formatBackupSites(backupSites) {
    const callSites = backupSites.call || [];
    const recordingSites = backupSites.recording || [];
    const vodSites = backupSites.vod || [];

    if (callSites.length === 0 && recordingSites.length === 0 && vodSites.length === 0) {
        return '<span class="text-gray-400">无</span>';
    }

    let html = '';
    if (callSites.length > 0) {
        html += `<div class="mb-1">
            <span class="text-xs text-gray-500 flex items-center mb-1">
                <i class="fas fa-phone mr-1"></i>呼叫备份:
            </span>
            <div class="flex flex-wrap gap-1">
                ${callSites.map(site =>
                    `<span class="inline-block bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs" title="${site.sitecode}">${site.siteName}</span>`
                ).join('')}
            </div>
        </div>`;
    }
    if (recordingSites.length > 0) {
        html += `<div class="mb-1">
            <span class="text-xs text-gray-500 flex items-center mb-1">
                <i class="fas fa-record-vinyl mr-1"></i>录制备份:
            </span>
            <div class="flex flex-wrap gap-1">
                ${recordingSites.map(site =>
                    `<span class="inline-block bg-green-100 text-green-700 px-2 py-1 rounded text-xs" title="${site.sitecode}">${site.siteName}</span>`
                ).join('')}
            </div>
        </div>`;
    }
    if (vodSites.length > 0) {
        html += `<div>
            <span class="text-xs text-gray-500 flex items-center mb-1">
                <i class="fas fa-play-circle mr-1"></i>点播备份:
            </span>
            <div class="flex flex-wrap gap-1">
                ${vodSites.map(site =>
                    `<span class="inline-block bg-orange-100 text-orange-700 px-2 py-1 rounded text-xs" title="${site.sitecode}">${site.siteName}</span>`
                ).join('')}
            </div>
        </div>`;
    }

    return html;
}

function formatServiceDetails(site) {
    const callServers = site.servers?.call || [];
    const recordingServers = site.servers?.recording || [];
    const vodServers = site.servers?.vod || [];

    if (callServers.length === 0 && recordingServers.length === 0 && vodServers.length === 0) {
        return '<span class="text-gray-400">无服务</span>';
    }

    return `
        <div class="server-grid grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- 呼叫服务 -->
            <div class="service-column">
                <div class="flex items-center justify-between mb-3">
                    <span class="text-sm font-medium text-gray-700 flex items-center">
                        <i class="fas fa-phone text-blue-600 mr-2"></i>
                        呼叫服务 (${callServers.length})
                    </span>
                </div>
                <div class="servers-list">
                    ${callServers.length > 0 ? callServers.map(server => `
                        <div class="server-item bg-blue-50 border-blue-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="flex-1 min-w-0">
                                        <div class="font-medium text-gray-900 text-sm truncate">${server.name}</div>
                                        <div class="text-xs text-gray-500">
                                            <span class="server-status ${getServerStatusClass(server.status)}">
                                                <i class="fas ${getServerStatusIcon(server.status)} mr-1"></i>
                                                ${server.status}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn-text btn-xs" onclick="showServerDetail('${server.name}')" title="查看详情">
                                    <i class="fas fa-external-link-alt"></i>
                                </button>
                            </div>
                        </div>
                    `).join('') : '<div class="text-center text-gray-500 py-4 bg-gray-50 rounded text-xs">暂无呼叫服务</div>'}
                </div>
            </div>

            <!-- 录制服务 -->
            <div class="service-column">
                <div class="flex items-center justify-between mb-3">
                    <span class="text-sm font-medium text-gray-700 flex items-center">
                        <i class="fas fa-record-vinyl text-green-600 mr-2"></i>
                        录制服务 (${recordingServers.length})
                    </span>
                </div>
                <div class="servers-list">
                    ${recordingServers.length > 0 ? recordingServers.map(server => `
                        <div class="server-item bg-green-50 border-green-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="flex-1 min-w-0">
                                        <div class="font-medium text-gray-900 text-sm truncate">${server.name}</div>
                                        <div class="text-xs text-gray-500">
                                            <span class="server-status ${getServerStatusClass(server.status)}">
                                                <i class="fas ${getServerStatusIcon(server.status)} mr-1"></i>
                                                ${server.status}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn-text btn-xs" onclick="showServerDetail('${server.name}')" title="查看详情">
                                    <i class="fas fa-external-link-alt"></i>
                                </button>
                            </div>
                        </div>
                    `).join('') : '<div class="text-center text-gray-500 py-4 bg-gray-50 rounded text-xs">暂无录制服务</div>'}
                </div>
            </div>

            <!-- 点播服务 -->
            <div class="service-column">
                <div class="flex items-center justify-between mb-3">
                    <span class="text-sm font-medium text-gray-700 flex items-center">
                        <i class="fas fa-play-circle text-orange-600 mr-2"></i>
                        点播服务 (${vodServers.length})
                    </span>
                </div>
                <div class="servers-list">
                    ${vodServers.length > 0 ? vodServers.map(server => `
                        <div class="server-item bg-orange-50 border-orange-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="flex-1 min-w-0">
                                        <div class="font-medium text-gray-900 text-sm truncate">${server.name}</div>
                                        <div class="text-xs text-gray-500">
                                            <span class="server-status ${getServerStatusClass(server.status)}">
                                                <i class="fas ${getServerStatusIcon(server.status)} mr-1"></i>
                                                ${server.status}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn-text btn-xs" onclick="showServerDetail('${server.name}')" title="查看详情">
                                    <i class="fas fa-external-link-alt"></i>
                                </button>
                            </div>
                        </div>
                    `).join('') : '<div class="text-center text-gray-500 py-4 bg-gray-50 rounded text-xs">暂无点播服务</div>'}
                </div>
            </div>
        </div>
    `;
}

function showServerDetail(serverName) {
    Message.info(`正在跳转到服务 "${serverName}" 的详情页面...`);
    // 这里可以实现实际的跳转逻辑
}

function formatBackupSitesDetailed(site) {
    const callSites = site.backupSites?.call || [];
    const recordingSites = site.backupSites?.recording || [];
    const vodSites = site.backupSites?.vod || [];

    return `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 呼叫备份站点 -->
            <div class="backup-service-column">
                <div class="flex items-center justify-between mb-3">
                    <span class="text-sm font-medium text-gray-700 flex items-center">
                        <i class="fas fa-phone text-blue-600 mr-2"></i>
                        呼叫备份站点 (${callSites.length})
                    </span>
                    <button class="btn-text btn-sm text-blue-600" onclick="showAddBackupModal('call')">
                        <i class="fas fa-plus mr-1"></i>添加
                    </button>
                </div>
                <div class="backup-sites-list">
                    ${callSites.length > 0 ? callSites.map((backupSite, index) => `
                        <div class="backup-site-item bg-blue-50 border-blue-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="flex items-center justify-center w-5 h-5 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                                        ${index + 1}
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="font-medium text-gray-900 text-sm truncate">${backupSite.siteName}</div>
                                        <div class="text-xs text-gray-500">${backupSite.sitecode}</div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <button class="btn-text btn-xs" onclick="moveBackupSite('call', ${index}, -1)" ${index === 0 ? 'disabled' : ''} title="上移">
                                        <i class="fas fa-arrow-up"></i>
                                    </button>
                                    <button class="btn-text btn-xs" onclick="moveBackupSite('call', ${index}, 1)" ${index === callSites.length - 1 ? 'disabled' : ''} title="下移">
                                        <i class="fas fa-arrow-down"></i>
                                    </button>
                                    <button class="btn-text btn-xs text-red-600" onclick="removeBackupSite('call', '${backupSite.sitecode}')" title="移除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `).join('') : '<div class="text-center text-gray-500 py-6 bg-gray-50 rounded-lg text-sm">暂无呼叫备份站点</div>'}
                </div>
            </div>

            <!-- 录制备份站点 -->
            <div class="backup-service-column">
                <div class="flex items-center justify-between mb-3">
                    <span class="text-sm font-medium text-gray-700 flex items-center">
                        <i class="fas fa-record-vinyl text-green-600 mr-2"></i>
                        录制备份站点 (${recordingSites.length})
                    </span>
                    <button class="btn-text btn-sm text-green-600" onclick="showAddBackupModal('recording')">
                        <i class="fas fa-plus mr-1"></i>添加
                    </button>
                </div>
                <div class="backup-sites-list">
                    ${recordingSites.length > 0 ? recordingSites.map((backupSite, index) => `
                        <div class="backup-site-item bg-green-50 border-green-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="flex items-center justify-center w-5 h-5 bg-green-100 text-green-600 rounded-full text-xs font-medium">
                                        ${index + 1}
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="font-medium text-gray-900 text-sm truncate">${backupSite.siteName}</div>
                                        <div class="text-xs text-gray-500">${backupSite.sitecode}</div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <button class="btn-text btn-xs" onclick="moveBackupSite('recording', ${index}, -1)" ${index === 0 ? 'disabled' : ''} title="上移">
                                        <i class="fas fa-arrow-up"></i>
                                    </button>
                                    <button class="btn-text btn-xs" onclick="moveBackupSite('recording', ${index}, 1)" ${index === recordingSites.length - 1 ? 'disabled' : ''} title="下移">
                                        <i class="fas fa-arrow-down"></i>
                                    </button>
                                    <button class="btn-text btn-xs text-red-600" onclick="removeBackupSite('recording', '${backupSite.sitecode}')" title="移除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `).join('') : '<div class="text-center text-gray-500 py-6 bg-gray-50 rounded-lg text-sm">暂无录制备份站点</div>'}
                </div>
            </div>

            <!-- 点播备份站点 -->
            <div class="backup-service-column">
                <div class="flex items-center justify-between mb-3">
                    <span class="text-sm font-medium text-gray-700 flex items-center">
                        <i class="fas fa-play-circle text-orange-600 mr-2"></i>
                        点播备份站点 (${vodSites.length})
                    </span>
                    <button class="btn-text btn-sm text-orange-600" onclick="showAddBackupModal('vod')">
                        <i class="fas fa-plus mr-1"></i>添加
                    </button>
                </div>
                <div class="backup-sites-list">
                    ${vodSites.length > 0 ? vodSites.map((backupSite, index) => `
                        <div class="backup-site-item bg-orange-50 border-orange-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="flex items-center justify-center w-5 h-5 bg-orange-100 text-orange-600 rounded-full text-xs font-medium">
                                        ${index + 1}
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="font-medium text-gray-900 text-sm truncate">${backupSite.siteName}</div>
                                        <div class="text-xs text-gray-500">${backupSite.sitecode}</div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <button class="btn-text btn-xs" onclick="moveBackupSite('vod', ${index}, -1)" ${index === 0 ? 'disabled' : ''} title="上移">
                                        <i class="fas fa-arrow-up"></i>
                                    </button>
                                    <button class="btn-text btn-xs" onclick="moveBackupSite('vod', ${index}, 1)" ${index === vodSites.length - 1 ? 'disabled' : ''} title="下移">
                                        <i class="fas fa-arrow-down"></i>
                                    </button>
                                    <button class="btn-text btn-xs text-red-600" onclick="removeBackupSite('vod', '${backupSite.sitecode}')" title="移除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `).join('') : '<div class="text-center text-gray-500 py-6 bg-gray-50 rounded-lg text-sm">暂无点播备份站点</div>'}
                </div>
            </div>
        </div>
    `;
}

function formatRules(rules) {
    if (rules.length === 0) {
        return '<span class="text-gray-400">无规则</span>';
    }

    const ruleTypes = {
        'meeting': { icon: 'fas fa-video', color: 'red', name: '会议' },
        'ip': { icon: 'fas fa-network-wired', color: 'yellow', name: 'IP' },
        'department': { icon: 'fas fa-sitemap', color: 'blue', name: '部门' }
    };

    const grouped = rules.reduce((acc, rule) => {
        if (!acc[rule.type]) acc[rule.type] = 0;
        acc[rule.type]++;
        return acc;
    }, {});

    return Object.entries(grouped).map(([type, count]) => {
        const config = ruleTypes[type];
        return `<span class="inline-flex items-center px-2 py-1 rounded text-xs bg-${config.color}-100 text-${config.color}-700 mr-1 mb-1">
            <i class="${config.icon} mr-1"></i>
            ${config.name} ${count}
        </span>`;
    }).join('');
}

// 站点详情侧边栏
function showSitePanel(siteId) {
    const site = mockSites.find(s => s.id === siteId);
    if (!site) {
        Message.error('站点不存在');
        return;
    }

    currentEditingSite = { ...site };

    const panel = document.getElementById('sitePanel');
    const title = document.getElementById('sitePanelTitle');
    const content = document.getElementById('sitePanelContent');
    const actions = document.getElementById('sitePanelActions');

    title.textContent = '编辑站点';
    actions.classList.remove('hidden');
    content.innerHTML = generateEditForm(site);

    panel.classList.remove('translate-x-full');
}

function hideSitePanel() {
    const panel = document.getElementById('sitePanel');
    panel.classList.add('translate-x-full');
    currentEditingSite = null;
}



function generateEditForm(site) {
    // 获取区域和网络选项
    let regionOptions = '<option value="">请选择区域</option>';
    let networkOptions = '<option value="">请选择网络</option>';

    if (typeof getRegions === 'function' && typeof getNetworks === 'function') {
        const regions = getRegions();
        const networks = getNetworks();

        regionOptions += regions.map(region =>
            `<option value="${region.name}" ${site.region === region.name ? 'selected' : ''}>${region.name}</option>`
        ).join('');

        networkOptions += networks.map(network =>
            `<option value="${network.name}" ${site.network === network.name ? 'selected' : ''}>${network.name}</option>`
        ).join('');
    } else {
        // 回退到默认选项
        regionOptions += `
            <option value="总部" ${site.region === '总部' ? 'selected' : ''}>总部</option>
            <option value="郑州" ${site.region === '郑州' ? 'selected' : ''}>郑州</option>
            <option value="武汉" ${site.region === '武汉' ? 'selected' : ''}>武汉</option>
            <option value="西安" ${site.region === '西安' ? 'selected' : ''}>西安</option>
        `;
        networkOptions += `
            <option value="内网" ${site.network === '内网' ? 'selected' : ''}>内网</option>
            <option value="DMZ" ${site.network === 'DMZ' ? 'selected' : ''}>DMZ</option>
            <option value="互联网" ${site.network === '互联网' ? 'selected' : ''}>互联网</option>
        `;
    }

    return `
        <form id="siteEditForm" class="space-y-6">
            <div class="form-group">
                <label class="form-label required">站点编码</label>
                <input type="text" class="form-control bg-gray-100" name="sitecode" value="${site.sitecode}" readonly>
                <div class="form-help">站点编码不可修改</div>
            </div>

            <div class="form-group">
                <label class="form-label required">站点名称</label>
                <input type="text" class="form-control" name="siteName" value="${site.siteName}" required>
            </div>

            <div class="form-group">
                <label class="form-label required">所属区域</label>
                <select class="form-control" name="region" required>
                    ${regionOptions}
                </select>
            </div>

            <div class="form-group">
                <label class="form-label required">所属网络</label>
                <select class="form-control" name="network" required>
                    ${networkOptions}
                </select>
            </div>
        </form>
    `;
}

function cancelSiteEdit() {
    hideSitePanel();
}

function saveSiteEdit() {
    const form = document.getElementById('siteEditForm');
    const formData = new FormData(form);

    if (!currentEditingSite) return;

    const newSiteName = formData.get('siteName');
    const newRegion = formData.get('region');
    const newNetwork = formData.get('network');

    // 验证站点名称唯一性（排除当前站点）
    if (mockSites.some(site => site.id !== currentEditingSite.id && site.siteName === newSiteName)) {
        Message.error('站点名称已存在，请使用其他站点名称');
        return;
    }

    // 更新站点数据
    const siteIndex = mockSites.findIndex(s => s.id === currentEditingSite.id);
    if (siteIndex > -1) {
        mockSites[siteIndex] = {
            ...mockSites[siteIndex],
            siteName: newSiteName,
            region: newRegion,
            network: newNetwork
        };

        loadSiteData();
        hideSitePanel();
        Message.success('站点信息更新成功');
    }
}





function showCannotDeleteInfo(siteId) {
    const site = mockSites.find(s => s.id === siteId);
    if (!site) return;

    const hasRules = mockRules[siteId] && mockRules[siteId].length > 0;
    const hasBoundServers = site.serverCount.call.total > 0 || site.serverCount.recording.total > 0 || site.serverCount.vod.total > 0;

    showCannotDeleteModal(site, hasRules, hasBoundServers);
}

function showCannotDeleteModal(site, hasRules, hasBoundServers) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';

    // 根据不同的原因生成不同的提示信息
    let reasonText = '';
    let steps = [];

    if (hasRules || hasBoundServers) {
        reasonText = '请移除所有绑定服务、接入规则后再删除。注意：以下站点正在使用本站点作为备份站点：xx站点、yy站点。';
    }

    modal.innerHTML = `
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">无法删除站点"${site.siteName}"</h3>
                    <button class="text-gray-400 hover:text-gray-600" onclick="this.closest('.fixed').remove()">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div class="mb-6">
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                        <div class="flex">
                            <i class="fas fa-exclamation-triangle text-yellow-600 mr-3 mt-0.5"></i>
                            <div>
                                <p class="text-sm text-yellow-700">${reasonText}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end">
                    <button type="button" class="btn btn-primary" onclick="this.closest('.fixed').remove()">
                        我知道了
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function addNewRule(siteId) {
    // 直接在当前页面显示添加规则弹窗
    showAddRuleModal(siteId);
}

function editRule(siteId, ruleIndex) {
    Message.info('编辑规则功能开发中...');
}

function deleteRule(siteId, ruleIndex) {
    Confirm.show(
        '确定要删除这条规则吗？',
        '确认删除',
        () => {
            if (mockRules[siteId] && mockRules[siteId][ruleIndex]) {
                mockRules[siteId].splice(ruleIndex, 1);

                // 记录当前展开状态
                const isExpanded = expandedSites.has(siteId);
                const activeTab = isExpanded ? document.querySelector(`#expanded-${siteId} .tab-nav-item.active`) : null;
                const activeTabType = activeTab ?
                    (activeTab.textContent.includes('服务信息') ? 'server' :
                     activeTab.textContent.includes('备份站点') ? 'backup' : 'rules') : null;

                // 如果站点已展开，刷新规则标签页内容
                if (isExpanded) {
                    loadTabContent(siteId, activeTabType || 'rules');
                }
                loadSiteData(); // 刷新表格

                // 恢复展开状态
                if (isExpanded) {
                    expandedSites.add(siteId);
                    const expandedRow = document.getElementById(`expanded-${siteId}`);
                    const siteRow = document.querySelector(`tr[data-site-id="${siteId}"]`);
                    const expandBtn = siteRow ? siteRow.querySelector('.expand-btn') : null;

                    if (expandedRow) expandedRow.classList.add('show');
                    if (siteRow) siteRow.classList.add('expanded');
                    if (expandBtn) expandBtn.classList.add('expanded');

                    // 重新激活标签页
                    if (activeTabType) {
                        switchTab(siteId, activeTabType);
                    }
                }

                Message.success('规则删除成功');
            }
        }
    );
}

// 工具函数
function getRuleTypeColor(type) {
    const colors = {
        'meeting': 'bg-red-500',
        'ip': 'bg-yellow-500',
        'department': 'bg-blue-500'
    };
    return colors[type] || 'bg-gray-500';
}

function getRuleTypeText(type) {
    const texts = {
        'meeting': '会议号',
        'ip': '终端IP',
        'department': '组织部门'
    };
    return texts[type] || type;
}

// 站点展开/收起功能
function toggleSiteExpansion(siteId) {
    const siteRow = document.querySelector(`tr[data-site-id="${siteId}"]`);
    const expandedRow = document.getElementById(`expanded-${siteId}`);
    const expandBtn = siteRow ? siteRow.querySelector('.expand-btn') : null;

    if (!siteRow || !expandedRow) {
        console.error('Site row or expanded row not found for siteId:', siteId);
        return;
    }

    if (expandedSites.has(siteId)) {
        // 收起
        expandedSites.delete(siteId);
        expandedRow.classList.remove('show');
        siteRow.classList.remove('expanded');
        if (expandBtn) expandBtn.classList.remove('expanded');
    } else {
        // 展开
        expandedSites.add(siteId);
        expandedRow.classList.add('show');
        siteRow.classList.add('expanded');
        if (expandBtn) expandBtn.classList.add('expanded');

        // 加载默认标签页内容（服务信息）
        loadTabContent(siteId, 'server');
    }
}

// 标签页切换功能
function switchTab(siteId, tabType) {
    // 更新标签页导航状态
    const tabNav = document.querySelector(`#expanded-${siteId} .tab-nav`);
    if (!tabNav) {
        console.error('Tab navigation not found for siteId:', siteId);
        return;
    }

    const tabNavItems = tabNav.querySelectorAll('.tab-nav-item');
    const tabPanes = document.querySelectorAll(`#expanded-${siteId} .tab-pane`);

    // 移除所有活动状态
    tabNavItems.forEach(item => item.classList.remove('active'));
    tabPanes.forEach(pane => pane.classList.remove('active'));

    // 设置当前标签页为活动状态
    const currentTabNav = Array.from(tabNavItems).find(item => {
        const tabName = tabType === 'server' ? '服务信息' :
                       tabType === 'backup' ? '备份站点' : '接入规则';
        return item.textContent.includes(tabName);
    });
    if (currentTabNav) {
        currentTabNav.classList.add('active');
    }

    const currentTabPane = document.getElementById(`tab-${tabType}-${siteId}`);
    if (currentTabPane) {
        currentTabPane.classList.add('active');
    }

    // 加载标签页内容
    loadTabContent(siteId, tabType);
}

// 加载标签页内容
function loadTabContent(siteId, tabType) {
    const site = mockSites.find(s => s.id === siteId);
    if (!site) return;

    const tabPane = document.getElementById(`tab-${tabType}-${siteId}`);
    if (!tabPane) return;

    switch (tabType) {
        case 'server':
            tabPane.innerHTML = generateServerTabContent(site);
            break;
        case 'backup':
            tabPane.innerHTML = generateBackupTabContent(site);
            break;
        case 'rules':
            tabPane.innerHTML = generateRulesTabContent(site);
            break;
    }
}

// 生成服务信息标签页内容
function generateServerTabContent(site) {
    return `
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <h4 class="text-lg font-medium text-gray-900">服务信息</h4>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
                ${formatServiceDetails(site)}
            </div>

            ${site.serverCount.call.total === 0 && site.serverCount.recording.total === 0 && site.serverCount.vod.total === 0 ?
                `<div class="text-center py-8">
                    <i class="fas fa-server text-4xl text-gray-300 mb-4"></i>
                    <p class="text-gray-500 text-lg mb-2">暂无绑定服务</p>
                    <p class="text-gray-400 text-sm">请先为此站点绑定服务</p>
                </div>` : ''}
        </div>
    `;
}

// 生成备份站点标签页内容
function generateBackupTabContent(site) {
    // 设置当前备份站点ID，供备份站点管理函数使用
    currentBackupSiteId = site.id;

    return `
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <h4 class="text-lg font-medium text-gray-900">备份站点配置</h4>
                <div class="text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-lg">
                    <i class="fas fa-info-circle mr-1"></i>
                    资源不可用时将使用备份站点，优先使用排序靠前的站点。如果使用86主站点资源，也需将86主站点添加为备份站点。
                </div>
            </div>

            <div class="space-y-4">
                ${formatBackupSitesDetailed(site)}
            </div>
        </div>
    `;
}

// 生成接入规则标签页内容
function generateRulesTabContent(site) {
    const rules = mockRules[site.id] || [];

    return `
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <h4 class="text-lg font-medium text-gray-900">接入规则配置</h4>
                <div class="flex items-center space-x-3">
                    <button class="btn btn-primary btn-sm" onclick="addNewRule(${site.id})">
                        <i class="fas fa-plus mr-1"></i>
                        添加规则
                    </button>
                    <button class="btn btn-success btn-sm" onclick="location.href='object-rules.html'">
                        <i class="fas fa-list mr-1"></i>
                        管理所有规则
                    </button>
                </div>
            </div>

            <div>
                <h5 class="text-md font-medium text-gray-700 mb-4">已配置规则 (${rules.length})</h5>
                ${rules.length > 0 ? `
                    <div class="space-y-3">
                        ${rules.map((rule, index) => `
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 rounded-full ${getRuleTypeColor(rule.type)}"></div>
                                        <div>
                                            <div class="font-medium text-gray-900">${rule.name || rule.value}</div>
                                            <div class="text-sm text-gray-500">${getRuleTypeText(rule.type)}: ${rule.value}</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button class="btn-text btn-sm" onclick="editRule(${site.id}, ${index})" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-text btn-sm text-red-600" onclick="deleteRule(${site.id}, ${index})" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                ` : `
                    <div class="text-center text-gray-500 py-8">
                        <i class="fas fa-cogs text-4xl mb-4 opacity-50"></i>
                        <p class="text-lg mb-2">暂无配置规则</p>
                        <p class="text-sm">点击"添加规则"开始配置站点规则</p>
                    </div>
                `}
            </div>
        </div>
    `;
}

// 备份站点管理功能
let currentBackupType = '';
let currentBackupSiteId = null;

function showAddBackupModal(type) {
    // 获取当前站点（优先从侧边栏，然后从展开的站点）
    const currentSite = currentEditingSite ||
                       (currentBackupSiteId ? mockSites.find(s => s.id === currentBackupSiteId) : null);

    if (!currentSite) {
        Message.error('请先选择站点');
        return;
    }

    currentBackupType = type;

    // 获取可选的备份站点
    const currentBackupCodes = (currentSite.backupSites[type] || []).map(backup => backup.sitecode);
    const availableSites = mockSites.filter(site =>
        site.sitecode !== currentSite.sitecode &&
        !currentBackupCodes.includes(site.sitecode) &&
        site.capabilities.includes(type)
    );

    if (availableSites.length === 0) {
        Message.warning(`没有可用的${type === 'call' ? '呼叫' : '录播'}备份站点`);
        return;
    }

    // 创建添加备份站点模态框
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
    modal.innerHTML = `
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">添加${type === 'call' ? '呼叫' : '录播'}备份站点</h3>
                    <button class="text-gray-400 hover:text-gray-600" onclick="this.closest('.fixed').remove()">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div class="form-group">
                    <label class="form-label required">选择备份站点</label>
                    <select class="form-control" id="addBackupSiteSelect" required>
                        <option value="">请选择站点</option>
                        ${availableSites.map(site => {
                            const capabilities = site.capabilities.map(cap => {
                                switch(cap) {
                                    case 'call': return '呼叫';
                                    case 'recording': return '录制';
                                    case 'vod': return '点播';
                                    default: return cap;
                                }
                            }).join('、');
                            return `<option value="${site.sitecode}">${site.siteName} (${site.sitecode}) - ${capabilities}服务</option>`;
                        }).join('')}
                    </select>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.fixed').remove()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="handleAddBackup('${type}', this)">添加</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function handleAddBackup(type, button) {
    const modal = button.closest('.fixed');
    const select = modal.querySelector('#addBackupSiteSelect');
    const backupSiteCode = select.value;

    if (!backupSiteCode) {
        Message.error('请选择备份站点');
        return;
    }

    const backupSite = mockSites.find(s => s.sitecode === backupSiteCode);
    if (!backupSite) {
        Message.error('选择的站点不存在');
        return;
    }

    // 添加备份站点到当前站点
    const targetSite = currentEditingSite ||
                      (currentBackupSiteId ? mockSites.find(s => s.id === currentBackupSiteId) : null);

    if (!targetSite) {
        Message.error('目标站点不存在');
        return;
    }

    targetSite.backupSites[type].push({
        sitecode: backupSiteCode,
        siteName: backupSite.siteName
    });

    // 同步到mockSites数据
    const siteIndex = mockSites.findIndex(s => s.id === targetSite.id);
    if (siteIndex > -1) {
        mockSites[siteIndex].backupSites[type] = [...targetSite.backupSites[type]];
    }

    // 记录当前展开状态
    const isExpanded = expandedSites.has(targetSite.id);
    const activeTab = isExpanded ? document.querySelector(`#expanded-${targetSite.id} .tab-nav-item.active`) : null;
    const activeTabType = activeTab ?
        (activeTab.textContent.includes('服务信息') ? 'server' :
         activeTab.textContent.includes('备份站点') ? 'backup' : 'rules') : null;

    // 刷新显示内容
    if (currentEditingSite) {
        // 如果在侧边栏中，刷新侧边栏内容
        showSitePanel(targetSite.id);
    } else if (isExpanded) {
        // 如果在展开的标签页中，刷新标签页内容
        loadTabContent(targetSite.id, activeTabType || 'backup');
    }

    // 刷新主页面表格
    loadSiteData();

    // 恢复展开状态
    if (isExpanded) {
        expandedSites.add(targetSite.id);
        const expandedRow = document.getElementById(`expanded-${targetSite.id}`);
        const siteRow = document.querySelector(`tr[data-site-id="${targetSite.id}"]`);
        const expandBtn = siteRow ? siteRow.querySelector('.expand-btn') : null;

        if (expandedRow) expandedRow.classList.add('show');
        if (siteRow) siteRow.classList.add('expanded');
        if (expandBtn) expandBtn.classList.add('expanded');

        // 重新激活标签页
        if (activeTabType) {
            switchTab(targetSite.id, activeTabType);
        }
    }

    // 关闭模态框
    modal.remove();

    Message.success('备份站点添加成功');
}

function moveBackupSite(type, index, direction) {
    const targetSite = currentEditingSite ||
                      (currentBackupSiteId ? mockSites.find(s => s.id === currentBackupSiteId) : null);
    if (!targetSite) return;

    const backupSites = targetSite.backupSites[type];
    const newIndex = index + direction;

    if (newIndex < 0 || newIndex >= backupSites.length) return;

    // 交换位置
    const temp = backupSites[index];
    backupSites[index] = backupSites[newIndex];
    backupSites[newIndex] = temp;

    // 同步到mockSites数据
    const siteIndex = mockSites.findIndex(s => s.id === targetSite.id);
    if (siteIndex > -1) {
        mockSites[siteIndex].backupSites[type] = [...targetSite.backupSites[type]];
    }

    // 记录当前展开状态
    const isExpanded = expandedSites.has(targetSite.id);
    const activeTab = isExpanded ? document.querySelector(`#expanded-${targetSite.id} .tab-nav-item.active`) : null;
    const activeTabType = activeTab ?
        (activeTab.textContent.includes('服务信息') ? 'server' :
         activeTab.textContent.includes('备份站点') ? 'backup' : 'rules') : null;

    // 刷新显示内容
    if (currentEditingSite) {
        // 如果在侧边栏中，刷新侧边栏内容
        showSitePanel(targetSite.id);
    } else if (isExpanded) {
        // 如果在展开的标签页中，刷新标签页内容
        loadTabContent(targetSite.id, activeTabType || 'backup');
    }

    // 刷新主页面表格
    loadSiteData();

    // 恢复展开状态
    if (isExpanded) {
        expandedSites.add(targetSite.id);
        const expandedRow = document.getElementById(`expanded-${targetSite.id}`);
        const siteRow = document.querySelector(`tr[data-site-id="${targetSite.id}"]`);
        const expandBtn = siteRow ? siteRow.querySelector('.expand-btn') : null;

        if (expandedRow) expandedRow.classList.add('show');
        if (siteRow) siteRow.classList.add('expanded');
        if (expandBtn) expandBtn.classList.add('expanded');

        // 重新激活标签页
        if (activeTabType) {
            switchTab(targetSite.id, activeTabType);
        }
    }

    Message.success('备份站点顺序调整成功');
}

function removeBackupSite(type, siteCode) {
    const targetSite = currentEditingSite ||
                      (currentBackupSiteId ? mockSites.find(s => s.id === currentBackupSiteId) : null);
    if (!targetSite) return;

    const backupSite = targetSite.backupSites[type].find(backup => backup.sitecode === siteCode);
    const siteName = backupSite ? backupSite.siteName : siteCode;

    Confirm.show(
        `确定要移除${type === 'call' ? '呼叫' : '录播'}备份站点 "${siteName}" 吗？`,
        '确认移除',
        () => {
            const index = targetSite.backupSites[type].findIndex(backup => backup.sitecode === siteCode);
            if (index > -1) {
                targetSite.backupSites[type].splice(index, 1);

                // 同步到mockSites数据
                const siteIndex = mockSites.findIndex(s => s.id === targetSite.id);
                if (siteIndex > -1) {
                    mockSites[siteIndex].backupSites[type] = [...targetSite.backupSites[type]];
                }

                // 记录当前展开状态
                const isExpanded = expandedSites.has(targetSite.id);
                const activeTab = isExpanded ? document.querySelector(`#expanded-${targetSite.id} .tab-nav-item.active`) : null;
                const activeTabType = activeTab ?
                    (activeTab.textContent.includes('服务信息') ? 'server' :
                     activeTab.textContent.includes('备份站点') ? 'backup' : 'rules') : null;

                // 刷新显示内容
                if (currentEditingSite) {
                    // 如果在侧边栏中，刷新侧边栏内容
                    showSitePanel(targetSite.id);
                } else if (isExpanded) {
                    // 如果在展开的标签页中，刷新标签页内容
                    loadTabContent(targetSite.id, activeTabType || 'backup');
                }

                loadSiteData();

                // 恢复展开状态
                if (isExpanded) {
                    expandedSites.add(targetSite.id);
                    const expandedRow = document.getElementById(`expanded-${targetSite.id}`);
                    const siteRow = document.querySelector(`tr[data-site-id="${targetSite.id}"]`);
                    const expandBtn = siteRow ? siteRow.querySelector('.expand-btn') : null;

                    if (expandedRow) expandedRow.classList.add('show');
                    if (siteRow) siteRow.classList.add('expanded');
                    if (expandBtn) expandBtn.classList.add('expanded');

                    // 重新激活标签页
                    if (activeTabType) {
                        switchTab(targetSite.id, activeTabType);
                    }
                }

                Message.success('备份站点移除成功');
            }
        }
    );
}

// 编辑备份站点功能已移除

// 添加规则功能
function showAddRuleModal(siteId) {
    const site = mockSites.find(s => s.id === siteId);
    if (!site) {
        Message.error('站点不存在');
        return;
    }

    // 创建添加规则模态框
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
    modal.innerHTML = `
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
            <div class="flex items-center justify-between p-6 border-b">
                <h3 class="text-lg font-semibold text-gray-900">为站点 "${site.siteName}" 添加规则</h3>
                <button class="text-gray-400 hover:text-gray-600" onclick="this.closest('.fixed').remove()">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="p-6 overflow-y-auto max-h-[60vh]">
                <form id="addRuleForm" onsubmit="handleAddRuleInSiteManagement(event)">
                    <div class="form-group">
                        <label class="form-label required">目标站点</label>
                        <select class="form-control" name="siteId" required onchange="checkSiteCapabilityWarning()">
                            <option value="">请选择站点</option>
                            ${mockSites.map(s => `
                                <option value="${s.id}" ${s.id === siteId ? 'selected' : ''}>${s.siteName} (${s.sitecode})</option>
                            `).join('')}
                        </select>
                        <div id="siteCapabilityWarning" class="hidden mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div class="flex items-start">
                                <i class="fas fa-exclamation-triangle text-yellow-600 mr-2 mt-0.5"></i>
                                <div class="text-sm text-yellow-800" id="warningText"></div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label required">规则类型</label>
                        <select class="form-control" name="ruleType" required onchange="updateRuleFormContent(this.value); checkSiteCapabilityWarning();">
                            <option value="">请选择规则类型</option>
                            <option value="meeting">会议号规则</option>
                            <option value="ip">IP规则</option>
                            <option value="department">部门规则</option>
                        </select>
                        <div id="ruleTypeHint" class="form-help mt-2 text-sm text-blue-600 hidden"></div>
                    </div>
                    <div id="ruleFormContent">
                        <!-- 动态表单内容 -->
                    </div>
                    <div class="flex justify-end space-x-3 pt-4 border-t">
                        <button type="button" class="btn btn-secondary" onclick="this.closest('.fixed').remove()">取消</button>
                        <button type="submit" class="btn btn-primary">添加规则</button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 检查站点能力警告（修复初始加载时不触发警告的问题）
    setTimeout(() => {
        checkSiteCapabilityWarning();
    }, 100);
}

function checkSiteCapabilityWarning() {
    const warningDiv = document.getElementById('siteCapabilityWarning');
    const warningText = document.getElementById('warningText');

    // 获取当前选择的站点和规则类型
    const siteSelect = document.querySelector('select[name="siteId"]');
    const ruleTypeSelect = document.querySelector('select[name="ruleType"]');
    const siteId = siteSelect ? parseInt(siteSelect.value) : null;
    const ruleType = ruleTypeSelect ? ruleTypeSelect.value : null;

    // 如果选择了站点，检查站点能力
    if (siteId) {
        const site = mockSites.find(s => s.id === siteId);
        if (site) {
            // 检查站点是否缺少任何服务能力
            const allCapabilities = ['call', 'recording', 'vod'];
            const missingCapabilities = allCapabilities.filter(cap => !site.capabilities.includes(cap));

            if (missingCapabilities.length > 0) {
                // 检查备份站点是否能提供缺失的能力
                const backupSites = site.backupSites || { call: [], recording: [], vod: [] };
                const stillMissing = missingCapabilities.filter(cap => {
                    const backupSiteList = backupSites[cap] || [];
                    return !backupSiteList.some(backupSite => {
                        const backupSiteCode = typeof backupSite === 'string' ? backupSite : backupSite.sitecode;
                        const actualBackupSite = mockSites.find(s => s.sitecode === backupSiteCode);
                        return actualBackupSite && actualBackupSite.capabilities.includes(cap);
                    });
                });

                if (stillMissing.length > 0) {
                    const capabilityNames = stillMissing.map(cap => {
                        switch(cap) {
                            case 'call': return '呼叫';
                            case 'recording': return '录制';
                            case 'vod': return '点播';
                            default: return cap;
                        }
                    }).join('、');
                    warningDiv.classList.remove('hidden');
                    warningText.textContent = `站点"${site.siteName}"缺少${capabilityNames}服务能力，请为该站点添加具备${capabilityNames}能力的备份站点。如已添加请忽略。`;
                } else {
                    warningDiv.classList.add('hidden');
                }
            } else {
                warningDiv.classList.add('hidden');
            }
        }
    } else {
        warningDiv.classList.add('hidden');
    }
}

function updateRuleFormContent(ruleType) {
    const formContent = document.getElementById('ruleFormContent');
    const ruleTypeHint = document.getElementById('ruleTypeHint');
    if (!formContent) return;

    // 显示规则类型提示
    let hintText = '';
    switch (ruleType) {
        case 'meeting':
            hintText = '接入该会议室的终端/用户通过会议室将分配的站点进行呼叫、录制';
            break;
        case 'ip':
            hintText = '使用该IP的终端/用户将通过IP分配的站点进行呼叫、点播';
            break;
        case 'department':
            hintText = '该部门及其所有下属部门中的终端/用户将通过部门分配的站点进行呼叫、点播';
            break;
        default:
            ruleTypeHint.classList.add('hidden');
    }
    
    if (hintText) {
        ruleTypeHint.textContent = hintText;
        ruleTypeHint.classList.remove('hidden');
    } else {
        ruleTypeHint.classList.add('hidden');
    }

    let formHTML = '';

    switch (ruleType) {
        case 'meeting':
            formHTML = `
                <div class="form-group">
                    <label class="form-label required">选择方式</label>
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="radio" name="meetingSelectionType" value="type" class="mr-2" onchange="toggleMeetingSelectionTypeInSiteManagement()" checked>
                            <span>会议室类型</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="meetingSelectionType" value="single" class="mr-2" onchange="toggleMeetingSelectionTypeInSiteManagement()">
                            <span>单个会议室</span>
                        </label>
                    </div>
                </div>

                <div id="meetingTypeSelectionSite" class="form-group">
                    <label class="form-label required">会议室类型</label>
                    <select class="form-control" name="meetingType" required>
                        <option value="">请选择会议室类型</option>
                        <option value="personal">个人会议室</option>
                        <option value="enterprise">企业会议室</option>
                        <option value="enterprise_random">企业随机会议室</option>
                        <option value="sdk">SDK会议室</option>
                        <option value="group">群组会议室</option>
                    </select>
                </div>

                <div id="singleMeetingSelectionSite" class="form-group hidden">
                    <label class="form-label required">会议室</label>
                    <div class="relative">
                        <input type="text" class="form-control" name="ruleValue" placeholder="输入会议室进行搜索..."
                               oninput="searchMeetingsInSiteManagement(this.value)" autocomplete="off">
                        <div class="absolute right-2 top-2">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <div class="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-b-lg shadow-lg z-10 hidden" id="meetingSearchResults">
                            <!-- 搜索结果 -->
                        </div>
                    </div>
                    <div class="text-sm text-gray-500 mt-1">仅支持个人/企业会议室</div>
                </div>

                <div class="form-group">
                    <label class="form-label">规则名称</label>
                    <input type="text" class="form-control" name="meetingName" placeholder="规则名称（可选）">
                </div>
            `;
            break;
        case 'ip':
            formHTML = `
                <div class="form-group">
                    <label class="form-label required">IP地址/网段</label>
                    <input type="text" class="form-control" name="ruleValue" placeholder="如：************* 或 ***********/24" required>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-3">
                        <h5 class="font-medium text-blue-800 mb-2">地址格式说明：</h5>
                        <div class="text-sm text-blue-700 space-y-1">
                            <div><strong>IPv4:</strong> *******[/24], 1.2-4.3-5.4[/24]</div>
                            <div><strong>IPv6:</strong> 1:2:3:4::[/24], 1:2-4:3-5:4::[/24]</div>
                            <div class="text-blue-600">IPv6段禁止0开头 (例: 1:1:1::而非0001:0001:0001::)</div>
                        </div>
                        <h5 class="font-medium text-blue-800 mb-2 mt-3">生效时间：</h5>
                        <div class="text-sm text-blue-700 space-y-1">
                            <div><strong>IP段:</strong> 10分钟</div>
                            <div><strong>IP:</strong> 立即</div>
                        </div>
                        <h5 class="font-medium text-blue-800 mb-2 mt-3">注意事项：</h5>
                        <div class="text-sm text-blue-700 space-y-1">
                            <div><strong>范围地址重复:</strong> 添加"-"格式地址范围不校验重复</div>
                            <div>例: *******=86 和 1.2-4.3.4=87 都含 *******</div>
                            <div><strong>系统遵循最长匹配:</strong></div>
                            <div>例: *******/24=86 和 *******/32=87，******* 匹配 *******/32=87</div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">规则名称</label>
                    <input type="text" class="form-control" name="ipRuleName" placeholder="如：总部网段、开发环境网段（可选）">                </div>
            `;
            break;
        case 'department':
            formHTML = `
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-blue-600 mr-2 mt-0.5"></i>
                        <div class="text-sm text-blue-800">
                            <div class="font-medium mb-1">性能提示</div>
                            <div>按部门就近接入会影响服务性能。如非必要，请优先考虑其他方案。</div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label required">部门名称</label>
                    <input type="text" class="form-control" name="ruleValue" placeholder="如：技术部、市场部" required>
                </div>
            `;
            break;
        default:
            formHTML = '<div class="text-center text-gray-500 py-4">请先选择规则类型</div>';
    }

    formContent.innerHTML = formHTML;
}

// 模拟会议号数据（与object-rules.js保持一致）
const mockMeetings = {
    '123456789': 'VIP会议室',
    '987654321': '董事会会议室',
    '555666777': '郑州会议室',
    '888999000': '测试会议室',
    '111222333': '培训会议室'
};

// 会议号搜索功能
function searchMeetingsInSiteManagement(query) {
    const resultsContainer = document.getElementById('meetingSearchResults');
    if (!resultsContainer) return;

    if (!query || query.length < 2) {
        resultsContainer.classList.add('hidden');
        return;
    }

    // 搜索匹配的会议号
    const matches = Object.entries(mockMeetings).filter(([meetingId, meetingName]) =>
        meetingId.includes(query) || meetingName.toLowerCase().includes(query.toLowerCase())
    );

    if (matches.length === 0) {
        resultsContainer.classList.add('hidden');
        return;
    }

    resultsContainer.innerHTML = matches.map(([meetingId, meetingName]) => `
        <div class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
             onclick="selectMeetingInSiteManagement('${meetingId}', '${meetingName}')">
            <div class="font-medium text-gray-900">${meetingName}</div>
            <div class="text-sm text-gray-500">会议号: ${meetingId}</div>
        </div>
    `).join('');

    resultsContainer.classList.remove('hidden');
}

function selectMeetingInSiteManagement(meetingId, meetingName) {
    const ruleValueInput = document.querySelector('input[name="ruleValue"]');
    const meetingNameInput = document.querySelector('input[name="meetingName"]');
    const resultsContainer = document.getElementById('meetingSearchResults');

    if (ruleValueInput) ruleValueInput.value = meetingId;
    if (meetingNameInput) meetingNameInput.value = meetingName;
    if (resultsContainer) resultsContainer.classList.add('hidden');
}

// 切换会议室选择类型（站点管理版本）
function toggleMeetingSelectionTypeInSiteManagement() {
    const selectionType = document.querySelector('input[name="meetingSelectionType"]:checked')?.value;
    const typeSelection = document.getElementById('meetingTypeSelectionSite');
    const singleSelection = document.getElementById('singleMeetingSelectionSite');

    if (selectionType === 'type') {
        typeSelection?.classList.remove('hidden');
        singleSelection?.classList.add('hidden');

        // 清空单个会议室的输入
        const ruleValueInput = document.querySelector('input[name="ruleValue"]');
        if (ruleValueInput) ruleValueInput.value = '';

        // 设置会议室类型为必填
        const meetingTypeSelect = document.querySelector('select[name="meetingType"]');
        if (meetingTypeSelect) meetingTypeSelect.required = true;
    } else if (selectionType === 'single') {
        typeSelection?.classList.add('hidden');
        singleSelection?.classList.remove('hidden');

        // 清空会议室类型选择
        const meetingTypeSelect = document.querySelector('select[name="meetingType"]');
        if (meetingTypeSelect) {
            meetingTypeSelect.value = '';
            meetingTypeSelect.required = false;
        }

        // 设置单个会议室为必填
        const ruleValueInput = document.querySelector('input[name="ruleValue"]');
        if (ruleValueInput) ruleValueInput.required = true;
    }
}

// 处理添加规则表单提交
function handleAddRuleInSiteManagement(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const ruleType = formData.get('ruleType');
    const meetingSelectionType = formData.get('meetingSelectionType');
    const meetingType = formData.get('meetingType');
    const ruleValue = formData.get('ruleValue');
    const meetingName = formData.get('meetingName');
    const ipRuleName = formData.get('ipRuleName');
    const siteId = parseInt(formData.get('siteId'));

    // 验证必填字段
    let actualRuleValue = ruleValue;
    if (ruleType === 'meeting') {
        if (meetingSelectionType === 'type') {
            if (!meetingType) {
                Message.error('请选择会议室类型');
                return;
            }
            actualRuleValue = meetingType;
        } else if (meetingSelectionType === 'single') {
            if (!ruleValue) {
                Message.error('请选择会议室');
                return;
            }
        }
    }

    if (!ruleType || !actualRuleValue || !siteId) {
        Message.error('请填写完整的规则信息');
        return;
    }

    // 查找站点信息
    const site = mockSites.find(s => s.id === siteId);
    if (!site) {
        Message.error('选择的站点不存在');
        return;
    }

    // 创建新规则
    let ruleName;
    switch (ruleType) {
        case 'meeting':
            if (meetingSelectionType === 'type') {
                // 会议室类型的显示名称
                const meetingTypeNames = {
                    'personal': '个人会议室',
                    'enterprise': '企业会议室',
                    'enterprise_random': '企业随机会议室',
                    'sdk': 'SDK会议室',
                    'group': '群组会议室'
                };
                ruleName = meetingName || meetingTypeNames[meetingType] || meetingType;
            } else {
                // 单个会议室的显示名称
                ruleName = meetingName || mockMeetings[actualRuleValue] || actualRuleValue;
            }
            break;
        case 'ip':
            ruleName = ipRuleName || actualRuleValue;
            break;
        case 'department':
            ruleName = actualRuleValue;
            break;
        default:
            ruleName = actualRuleValue;
    }

    // 添加到模拟规则数据中（这里应该调用API）
    const newRule = {
        id: Date.now(),
        type: ruleType,
        value: actualRuleValue,
        name: ruleName,
        siteId: siteId,
        siteName: site.siteName,
        sitecode: site.sitecode
    };

    // 更新站点的规则数据
    if (!mockRules[siteId]) {
        mockRules[siteId] = [];
    }
    mockRules[siteId].push(newRule);

    // 记录当前展开状态
    const isExpanded = expandedSites.has(siteId);
    const activeTab = isExpanded ? document.querySelector(`#expanded-${siteId} .tab-nav-item.active`) : null;
    const activeTabType = activeTab ?
        (activeTab.textContent.includes('服务信息') ? 'server' :
         activeTab.textContent.includes('备份站点') ? 'backup' : 'rules') : null;

    // 关闭模态框
    event.target.closest('.fixed').remove();

    // 刷新站点数据显示
    loadSiteData();

    // 恢复展开状态
    if (isExpanded) {
        expandedSites.add(siteId);
        const expandedRow = document.getElementById(`expanded-${siteId}`);
        const siteRow = document.querySelector(`tr[data-site-id="${siteId}"]`);
        const expandBtn = siteRow ? siteRow.querySelector('.expand-btn') : null;

        if (expandedRow) expandedRow.classList.add('show');
        if (siteRow) siteRow.classList.add('expanded');
        if (expandBtn) expandBtn.classList.add('expanded');

        // 重新激活标签页
        if (activeTabType) {
            switchTab(siteId, activeTabType);
        }
    }

    // 如果规则配置模态框还开着，也刷新它
    const rulesModal = document.getElementById('rulesModal');
    if (rulesModal && !rulesModal.classList.contains('hidden')) {
        showRulesModal(siteId);
    }

    Message.success('规则添加成功');
}

// 获取规则类型所需的能力
function getRuleRequiredCapabilities(ruleType) {
    switch (ruleType) {
        case 'meeting':
            return ['call', 'recording', 'vod']; // 会议号规则需要呼叫、录制和点播能力
        case 'ip':
            return ['call']; // IP规则主要用于呼叫路由
        case 'department':
            return ['call']; // 部门规则主要用于呼叫路由
        default:
            return [];
    }
}

// 检查站点是否具备处理规则的完整能力（包括备份站点）
function checkSiteCapabilityForRule(site, ruleType) {
    const requiredCapabilities = getRuleRequiredCapabilities(ruleType);
    const missingCapabilities = requiredCapabilities.filter(cap => !site.capabilities.includes(cap));

    if (missingCapabilities.length === 0) {
        return { hasCapability: true, message: '' };
    }

    // 检查备份站点能力
    const backupSites = site.backupSites || { call: [], recording: [], vod: [] };
    const missingAfterBackup = missingCapabilities.filter(cap => {
        const backupSiteList = backupSites[cap] || [];
        return !backupSiteList.some(backupSite => {
            const backupSiteCode = typeof backupSite === 'string' ? backupSite : backupSite.sitecode;
            const actualBackupSite = mockSites.find(s => s.sitecode === backupSiteCode);
            return actualBackupSite && actualBackupSite.capabilities.includes(cap);
        });
    });

    if (missingAfterBackup.length === 0) {
        return { hasCapability: true, message: '' };
    }

    // 生成统一的提示信息
    const capabilityNames = missingAfterBackup.map(cap => {
        switch(cap) {
            case 'call': return '呼叫';
            case 'recording': return '录制';
            case 'vod': return '点播';
            default: return cap;
        }
    }).join('、');
    const message = `站点"${site.siteName}"缺少${capabilityNames}服务能力，请为该站点添加具备${capabilityNames}能力的备份站点。如已添加请忽略。`;

    return { hasCapability: false, message };
}
