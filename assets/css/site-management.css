/* 站点管理模块样式 */

/* 自定义 Tooltip 样式 */
.tooltip {
  position: relative;
  display: inline-flex;
  align-items: center;
}

.tooltip .tooltip-icon {
  cursor: help;
  color: var(--text-secondary);
  transition: color 0.3s;
}

.tooltip .tooltip-icon:hover {
  color: var(--primary-color);
}

.tooltip .tooltip-text {
  visibility: hidden;
  position: fixed;
  background-color: rgba(0, 0, 0, 0.9);
  color: #fff;
  text-align: center;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  white-space: nowrap;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 99999;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* JS will handle the positioning of the tooltip and arrow */
.tooltip .tooltip-arrow {
  visibility: hidden;
  position: fixed;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 6px;
  border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent;
  z-index: 99999;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.tooltip:hover .tooltip-arrow {
  visibility: visible;
  opacity: 1;
}

/* 基础变量 */
:root {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;
  --border-color: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  --background-color: #f5f7fa;
  --card-background: #ffffff;
  --shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

/* 页面布局 */
.site-management-container {
  padding: 20px;
  background-color: var(--background-color);
  min-height: 100vh;
}

.page-header {
  background: var(--card-background);
  padding: 20px;
  border-radius: 8px;
  box-shadow: var(--shadow-base);
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 14px;
  margin: 0;
}

/* 工具栏 */
.toolbar {
  background: var(--card-background);
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: var(--shadow-base);
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 搜索框 */
.search-box {
  position: relative;
  min-width: 280px;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-placeholder);
}

/* 筛选器 */
.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  background: white;
  min-width: 120px;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s;
  gap: 6px;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

.btn-success {
  background-color: var(--success-color);
  border-color: var(--success-color);
  color: white;
}

.btn-warning {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
  color: white;
}

.btn-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
  color: white;
}

.btn-secondary {
  background-color: white;
  border-color: var(--border-color);
  color: var(--text-regular);
}

.btn-secondary:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-text {
  background: transparent;
  border: none;
  color: var(--primary-color);
  padding: 4px 8px;
}

.btn-text:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

/* 卡片样式 */
.card {
  background: var(--card-background);
  border-radius: 8px;
  box-shadow: var(--shadow-base);
  overflow: hidden;
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-lighter);
  background: #fafafa;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.card-body {
  padding: 20px;
}

/* 表格样式 */
.table-container {
  background: var(--card-background);
  border-radius: 8px;
  box-shadow: var(--shadow-base);
  overflow: hidden;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.table th {
  background: #fafafa;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-lighter);
}

.table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-lighter);
  color: var(--text-regular);
}

.table tbody tr:hover {
  background-color: #f5f7fa;
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* 状态标签 */
.status-tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  gap: 4px;
}

.status-online {
  background-color: rgba(103, 194, 58, 0.1);
  color: var(--success-color);
}

.status-offline {
  background-color: rgba(245, 108, 108, 0.1);
  color: var(--danger-color);
}

.status-maintenance {
  background-color: rgba(230, 162, 60, 0.1);
  color: var(--warning-color);
}

/* 能力标签 */
.capability-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.capability-tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  background-color: rgba(64, 158, 255, 0.1);
  color: var(--primary-color);
  border-radius: 4px;
  font-size: 12px;
  gap: 2px;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 20px;
  background: var(--card-background);
  border-radius: 8px;
  box-shadow: var(--shadow-base);
  margin-top: 20px;
}

.pagination-info {
  color: var(--text-secondary);
  font-size: 14px;
  margin-right: 16px;
}

.pagination-btn {
  padding: 6px 12px;
  border: 1px solid var(--border-color);
  background: white;
  color: var(--text-regular);
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.pagination-btn:hover:not(.disabled) {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.pagination-btn.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.pagination-btn.disabled {
  color: var(--text-placeholder);
  cursor: not-allowed;
}

/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 14px;
}

.form-label.required::after {
  content: '*';
  color: var(--danger-color);
  margin-left: 4px;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
}

.form-control.error {
  border-color: var(--danger-color);
}

.form-error {
  color: var(--danger-color);
  font-size: 12px;
  margin-top: 4px;
}

.form-help {
  color: var(--text-secondary);
  font-size: 12px;
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .site-management-container {
    padding: 10px;
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .search-box {
    min-width: auto;
  }

  .table-container {
    overflow-x: auto;
  }

  .table {
    min-width: 800px;
  }
}

/* 服务链接样式 */
.server-link {
  font-size: 0.875rem;
  color: #2563eb;
  cursor: pointer;
  text-decoration: underline;
  text-decoration-style: dotted;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.server-link:hover {
  color: #1d4ed8;
  text-decoration-style: solid;
  background-color: #eff6ff;
}

/* 可展开站点行样式 */
.site-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.site-row:hover {
  background-color: #f8fafc;
}

.site-row.expanded {
  background-color: #f1f5f9;
}

.expand-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.expand-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

.expand-btn i {
  transition: transform 0.2s ease;
}

.expand-btn.expanded i {
  transform: rotate(90deg);
}

/* 展开内容区域 */
.expanded-content {
  display: none;
  background-color: #f8fafc;
  border-top: 1px solid var(--border-lighter);
}

.expanded-content.show {
  display: table-row;
}

.expanded-content td {
  padding: 0;
  border-bottom: 1px solid var(--border-lighter);
}

.expanded-inner {
  padding: 20px;
}

/* 标签页样式 */
.tab-container {
  background: white;
  border-radius: 8px;
  box-shadow: var(--shadow-base);
  overflow: hidden;
}

.tab-nav {
  display: flex;
  background-color: #f8fafc;
  border-bottom: 1px solid var(--border-lighter);
}

.tab-nav-item {
  flex: 1;
  padding: 12px 16px;
  text-align: center;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.tab-nav-item:hover {
  color: var(--primary-color);
  background-color: rgba(64, 158, 255, 0.05);
}

.tab-nav-item.active {
  color: var(--primary-color);
  background-color: white;
}

.tab-nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--primary-color);
}

.tab-content {
  padding: 20px;
  min-height: 200px;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

/* 元数据管理页面样式 */
.metadata-tab-btn {
  padding: 12px 24px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.metadata-tab-btn:hover {
  color: var(--primary-color);
}

.metadata-tab-btn.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.metadata-tab-content {
  display: none;
}

.metadata-tab-content.active {
  display: block;
}

/* 备份站点三列布局样式 */
.backup-service-column {
  background: white;
  border: 1px solid var(--border-lighter);
  border-radius: 8px;
  padding: 16px;
  min-height: 300px;
}

.backup-sites-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 400px;
  overflow-y: auto;
}

.backup-site-item {
  padding: 12px;
  border: 1px solid;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.backup-site-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-xs {
  padding: 2px 6px;
  font-size: 11px;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .backup-service-column {
    min-height: 250px;
  }
}

@media (max-width: 768px) {
  .backup-service-column {
    min-height: 200px;
  }

  .backup-sites-list {
    max-height: 300px;
  }
}

/* Toggle Switch 样式 */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.toggle-switch input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.toggle-switch input:focus + .toggle-slider {
  box-shadow: 0 0 1px var(--primary-color);
}

/* 服务状态样式 */
.server-item {
  background: #f8fafc;
  border: 1px solid var(--border-lighter);
  border-radius: 6px;
  padding: 12px;
  transition: all 0.2s ease;
}

.server-item:hover {
  background: #f1f5f9;
  border-color: var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.server-status {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  margin-left: auto;
}

.server-status i {
  font-size: 10px;
}

.server-status.bg-green-100 {
  background-color: rgba(103, 194, 58, 0.1);
  color: var(--success-color);
}

.server-status.bg-red-100 {
  background-color: rgba(245, 108, 108, 0.1);
  color: var(--danger-color);
}

.server-status.bg-gray-100 {
  background-color: rgba(144, 147, 153, 0.1);
  color: var(--text-secondary);
}

/* 服务链接样式更新 */
.server-link {
  font-size: 0.875rem;
  color: #2563eb;
  cursor: pointer;
  text-decoration: underline;
  text-decoration-style: dotted;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.server-link:hover {
  color: #1d4ed8;
  text-decoration-style: solid;
  background-color: #eff6ff;
}
